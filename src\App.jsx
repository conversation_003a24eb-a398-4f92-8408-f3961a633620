import { Routes, Route } from "react-router-dom";
import { ConfigProvider } from "antd";
import RouterProvider from "./components/Router/RouterProvider";
import Layout from "./components/Layout/Layout";
import Home from "./pages/Home";
import About from "./pages/About";
import Contact from "./pages/Contact";
import Terms from "./pages/Terms";
import Generate from "./pages/Generate";
import History from "./pages/History";
import Error404 from "./pages/Error404";
import ErrorPage from "./pages/ErrorPage";
import ErrorBoundary from "./components/Common/ErrorBoundary";
import logger from "./utils/logger";

// Ant Design theme configuration
const theme = {
  token: {
    colorPrimary: "#3b82f6",
    colorSuccess: "#22c55e",
    colorWarning: "#f59e0b",
    colorError: "#ef4444",
    colorInfo: "#06b6d4",
    borderRadius: 8,
    fontFamily:
      "Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif",
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
      fontSize: 14,
      fontWeight: 500,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Card: {
      borderRadius: 12,
      boxShadow:
        "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
    },
  },
};

function App() {
  return (
    <ConfigProvider theme={theme}>
      <ErrorBoundary>
        <RouterProvider>
          <Layout>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/about" element={<About />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/terms" element={<Terms />} />
              <Route path="/generate" element={<Generate />} />
              <Route path="/history" element={<History />} />
              <Route path="/error" element={<ErrorPage />} />
              <Route path="*" element={<Error404 />} />
            </Routes>
          </Layout>
        </RouterProvider>
      </ErrorBoundary>
    </ConfigProvider>
  );
}

export default App;
