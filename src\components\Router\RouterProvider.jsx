import { BrowserRouter as Router } from "react-router-dom";
import { Suspense } from "react";
import { Spin } from "antd";

const RouterProvider = ({ children }) => {
  return (
    <Router>
      <Suspense 
        fallback={
          <div className="flex items-center justify-center min-h-screen">
            <Spin size="large" />
          </div>
        }
      >
        {children}
      </Suspense>
    </Router>
  );
};

export default RouterProvider;
