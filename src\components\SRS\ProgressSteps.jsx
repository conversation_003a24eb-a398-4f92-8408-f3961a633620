import React from 'react';
import { Card, Typography } from 'antd';
import { CheckCircle, Info } from 'lucide-react';
import { motion } from 'framer-motion';

const { Title, Text } = Typography;

const ProgressSteps = ({ steps, currentStep }) => {
  const completionPercentage = Math.round(((currentStep + 1) / steps.length) * 100);
  const estimatedTimeRemaining = Math.max(0, 8 - currentStep);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card mb-8 bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200 shadow-lg">
        <div className="mb-6">
          {/* Header Section */}
          <div className="flex justify-between items-center mb-6">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Title level={4} className="mb-1 text-gray-800 flex items-center">
                {steps[currentStep].icon}
                <span className="ml-3">{steps[currentStep].title}</span>
              </Title>
              <Text className="text-gray-600 text-base">
                {steps[currentStep].description}
              </Text>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-right"
            >
              <div className="text-3xl font-bold text-blue-600 mb-1">
                {currentStep + 1}/{steps.length}
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${completionPercentage}%` }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                  />
                </div>
                <Text className="text-sm text-gray-500 font-medium">
                  {completionPercentage}%
                </Text>
              </div>
            </motion.div>
          </div>

          {/* Custom Progress Visualization */}
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              {steps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={`flex items-center ${
                    index < steps.length - 1 ? 'flex-1' : ''
                  }`}
                >
                  {/* Step Circle with Animation */}
                  <motion.div
                    className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-3 transition-all duration-300 ${
                      index < currentStep
                        ? 'bg-green-500 border-green-500 text-white shadow-lg'
                        : index === currentStep
                        ? 'bg-blue-600 border-blue-600 text-white shadow-xl scale-110'
                        : 'bg-white border-gray-300 text-gray-400 hover:border-gray-400'
                    }`}
                    whileHover={{ scale: index <= currentStep ? 1.1 : 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {index < currentStep ? (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <CheckCircle size={18} />
                      </motion.div>
                    ) : (
                      <span className="text-sm font-bold">
                        {index + 1}
                      </span>
                    )}
                    
                    {/* Pulse animation for current step */}
                    {index === currentStep && (
                      <motion.div
                        className="absolute inset-0 rounded-full bg-blue-400"
                        animate={{ scale: [1, 1.3, 1], opacity: [0.7, 0, 0.7] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    )}
                  </motion.div>

                  {/* Animated Progress Line */}
                  {index < steps.length - 1 && (
                    <div className="flex-1 h-1 mx-3 bg-gray-200 rounded-full overflow-hidden">
                      <motion.div
                        className={`h-full rounded-full ${
                          index < currentStep
                            ? 'bg-gradient-to-r from-green-400 to-green-500'
                            : index === currentStep
                            ? 'bg-gradient-to-r from-blue-400 to-blue-500'
                            : 'bg-gray-200'
                        }`}
                        initial={{ width: '0%' }}
                        animate={{
                          width: index < currentStep
                            ? '100%'
                            : index === currentStep
                            ? '50%'
                            : '0%'
                        }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                      />
                    </div>
                  )}
                </motion.div>
              ))}
            </div>

            {/* Step Labels with Responsive Design */}
            <div className="hidden md:flex items-center justify-between mt-4">
              {steps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className={`text-center ${
                    index < steps.length - 1 ? 'flex-1' : ''
                  }`}
                  style={{ maxWidth: '140px' }}
                >
                  <div
                    className={`text-xs font-medium transition-colors duration-300 ${
                      index <= currentStep
                        ? 'text-gray-800'
                        : 'text-gray-400'
                    }`}
                  >
                    {step.title}
                  </div>
                  {index === currentStep && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-xs text-blue-600 font-medium mt-1"
                    >
                      Current
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Status Bar */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="flex flex-col md:flex-row justify-between items-start md:items-center pt-4 border-t border-blue-200 space-y-3 md:space-y-0"
        >
          <div className="flex flex-col md:flex-row items-start md:items-center space-y-2 md:space-y-0 md:space-x-6">
            <div className="flex items-center space-x-2">
              <motion.div
                className="w-2 h-2 bg-green-500 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <Text className="text-sm text-gray-600">Auto-saved every 30 seconds</Text>
            </div>
            <div className="flex items-center space-x-2">
              <Info size={14} className="text-blue-500" />
              <Text className="text-sm text-gray-600">
                Progress is automatically saved
              </Text>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              <span className="font-medium">Estimated time remaining:</span>{' '}
              <span className="text-blue-600 font-semibold">
                {estimatedTimeRemaining} min
              </span>
            </div>
            <div className="hidden md:block text-xs text-gray-400">
              Step {currentStep + 1} of {steps.length}
            </div>
          </div>
        </motion.div>
      </Card>
    </motion.div>
  );
};

export default ProgressSteps;
