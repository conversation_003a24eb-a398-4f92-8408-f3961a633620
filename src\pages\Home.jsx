import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Col, Card, Space, Statistic } from 'antd';
import { FileText, Zap, Clock, Shield, ArrowRight, CheckCircle, Star } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const Home = () => {
  const features = [
    {
      icon: <Zap className="text-electric-500" size={32} />,
      title: 'AI-Powered Generation',
      description: 'Advanced AI technology transforms your ideas into professional SRS documents in minutes.',
    },
    {
      icon: <FileText className="text-primary-600" size={32} />,
      title: 'IEEE 830 Compliant',
      description: 'Generated documents follow industry-standard IEEE 830 specifications for maximum compatibility.',
    },
    {
      icon: <Clock className="text-success-500" size={32} />,
      title: 'Save Time & Effort',
      description: 'Reduce documentation time from weeks to hours with our intelligent automation.',
    },
    {
      icon: <Shield className="text-cyan-500" size={32} />,
      title: 'Secure & Reliable',
      description: 'Your data is protected with enterprise-grade security and reliable cloud infrastructure.',
    },
  ];

  const benefits = [
    'Professional IEEE 830 compliant documents',
    'Multi-platform technology support',
    'Comprehensive requirement analysis',
    'Export to multiple formats (PDF, Word, HTML)',
    'Project history and version control',
    'Team collaboration features',
  ];

  const stats = [
    { title: 'Documents Generated', value: '10,000+', suffix: '' },
    { title: 'Time Saved', value: '500', suffix: 'hrs' },
    { title: 'Happy Users', value: '2,500+', suffix: '' },
    { title: 'Success Rate', value: '99.9', suffix: '%' },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="hero-gradient py-20 lg:py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 lg:px-8">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-white space-y-6"
              >
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Transform Ideas into
                  <span className="block text-cyan-300">Professional SRS</span>
                  <span className="block">Documents</span>
                </h1>
                <p className="text-xl lg:text-2xl text-blue-100 leading-relaxed">
                  Harness the power of AI to create comprehensive Software Requirements 
                  Specification documents that meet industry standards in minutes, not weeks.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 pt-4">
                  <Link to="/generate">
                    <Button 
                      type="primary" 
                      size="large"
                      className="h-14 px-8 text-lg font-semibold bg-white text-primary-600 border-none hover:bg-gray-100"
                      icon={<FileText size={20} />}
                    >
                      Start Creating SRS
                      <ArrowRight size={16} className="ml-2" />
                    </Button>
                  </Link>
                  <Link to="/about">
                    <Button 
                      size="large"
                      className="h-14 px-8 text-lg font-semibold bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600"
                    >
                      Learn More
                    </Button>
                  </Link>
                </div>
              </motion.div>
            </Col>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <div className="bg-gray-900/50 rounded-lg p-4 font-mono text-sm text-green-300">
                      <div>$ srs-generator --project "E-Commerce Platform"</div>
                      <div className="text-gray-400">Analyzing requirements...</div>
                      <div className="text-gray-400">Generating IEEE 830 compliant document...</div>
                      <div className="text-green-400">✓ SRS document generated successfully!</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Col>
          </Row>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <Row gutter={[32, 32]}>
            {stats.map((stat, index) => (
              <Col xs={12} lg={6} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    suffix={stat.suffix}
                    valueStyle={{ 
                      color: '#1e3a8a', 
                      fontSize: '2.5rem', 
                      fontWeight: 'bold' 
                    }}
                    className="[&_.ant-statistic-title]:text-gray-600 [&_.ant-statistic-title]:text-lg"
                  />
                </motion.div>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Why Choose Our <span className="gradient-text">SRS Generator</span>?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the future of software documentation with cutting-edge AI technology 
              that understands your requirements and delivers professional results.
            </p>
          </motion.div>

          <Row gutter={[32, 32]}>
            {features.map((feature, index) => (
              <Col xs={24} md={12} lg={6} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card 
                    className="card h-full text-center hover:shadow-2xl transition-all duration-300 border-0"
                    bodyStyle={{ padding: '2rem' }}
                  >
                    <div className="mb-4 flex justify-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-primary-100 to-electric-100 rounded-full flex items-center justify-center">
                        {feature.icon}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {feature.description}
                    </p>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gradient-to-r from-primary-50 to-electric-50">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                  Everything You Need for
                  <span className="block gradient-text">Professional Documentation</span>
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  Our comprehensive platform provides all the tools and features you need 
                  to create, manage, and export professional SRS documents.
                </p>
                <div className="space-y-4">
                  {benefits.map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircle className="text-success-500 flex-shrink-0" size={20} />
                      <span className="text-gray-700 text-lg">{benefit}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </Col>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">Document Preview</h3>
                      <div className="flex space-x-1">
                        <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                        <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-full"></div>
                      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                      <div className="h-8 bg-primary-100 rounded w-full"></div>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-100 rounded w-full"></div>
                        <div className="h-3 bg-gray-100 rounded w-4/5"></div>
                        <div className="h-3 bg-gray-100 rounded w-3/4"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Col>
          </Row>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-white">
              Ready to Transform Your
              <span className="block text-cyan-300">Documentation Process?</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Join thousands of developers and project managers who have revolutionized 
              their workflow with our AI-powered SRS generation platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
              <Link to="/generate">
                <Button 
                  type="primary" 
                  size="large"
                  className="h-14 px-8 text-lg font-semibold bg-gradient-to-r from-primary-600 to-electric-500 border-none hover:from-primary-700 hover:to-electric-600"
                  icon={<FileText size={20} />}
                >
                  Start Creating Now
                  <ArrowRight size={16} className="ml-2" />
                </Button>
              </Link>
              <Link to="/contact">
                <Button 
                  size="large"
                  className="h-14 px-8 text-lg font-semibold bg-transparent text-white border-2 border-gray-600 hover:bg-gray-800 hover:border-gray-500"
                >
                  Contact Sales
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
