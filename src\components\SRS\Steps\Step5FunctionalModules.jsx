import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Button,
  Select,
  Typography,
  Tag,
} from "antd";
import { Target, Plus, Minus } from "lucide-react";
import { motion } from "framer-motion";
import { PRIORITY_LEVELS } from "../../../constants/srsFormData";

const { Title } = Typography;
const { TextArea } = Input;

const Step5FunctionalModules = ({
  functionalModules,
  addFunctionalModule,
  removeFunctionalModule,
  updateFunctionalModule,
  form,
}) => {
  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case "High":
        return "red";
      case "Medium":
        return "orange";
      case "Low":
        return "green";
      default:
        return "blue";
    }
  };

  // Common module suggestions
  const modulesSuggestions = [
    "User Authentication & Authorization",
    "User Profile Management",
    "Dashboard & Analytics",
    "Content Management",
    "Payment Processing",
    "Notification System",
    "Search & Filtering",
    "Reporting & Export",
    "Admin Panel",
    "API Integration",
    "File Upload & Management",
    "Communication & Messaging",
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <Target className="mr-3 text-blue-600" size={24} />
          Functional Modules
        </Title>

        <div className="space-y-6">
          {functionalModules.map((module, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="bg-gray-50 border border-gray-200">
                <Row gutter={[16, 16]} align="top">
                  <Col xs={24} md={14}>
                    <Form.Item
                      label={`Module ${index + 1} Description`}
                      required
                      className="mb-0"
                    >
                      <TextArea
                        rows={3}
                        placeholder="Describe the functional module (e.g., User Authentication - handles login, registration, password reset)"
                        value={module.description}
                        onChange={(e) =>
                          updateFunctionalModule(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        className="rounded-lg"
                      />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={6}>
                    <Form.Item label="Priority Level" className="mb-0">
                      <Select
                        value={module.priority}
                        onChange={(value) =>
                          updateFunctionalModule(index, "priority", value)
                        }
                        className="w-full"
                        size="large"
                      >
                        {PRIORITY_LEVELS.map(({ value, label, color }) => (
                          <Select.Option key={value} value={value}>
                            <div className="flex items-center space-x-2">
                              <Tag color={color} className="m-0">
                                {label}
                              </Tag>
                            </div>
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={4}>
                    <div className="flex flex-col space-y-2">
                      <Button
                        type="primary"
                        icon={<Plus size={16} />}
                        onClick={addFunctionalModule}
                        className="w-full"
                        size="small"
                      >
                        Add Module
                      </Button>
                      {functionalModules.length > 1 && (
                        <Button
                          danger
                          icon={<Minus size={16} />}
                          onClick={() => removeFunctionalModule(index)}
                          className="w-full"
                          size="small"
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                  </Col>
                </Row>

                {/* Additional Module Details */}
                <Row gutter={[16, 16]} className="mt-4">
                  <Col xs={24} md={12}>
                    <Form.Item label="Key Features (Optional)" className="mb-0">
                      <Input
                        placeholder="List main features (e.g., Login, Signup, Password Reset)"
                        value={module.features || ""}
                        onChange={(e) =>
                          updateFunctionalModule(
                            index,
                            "features",
                            e.target.value
                          )
                        }
                        className="rounded-lg"
                      />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item label="Dependencies (Optional)" className="mb-0">
                      <Input
                        placeholder="Modules this depends on (e.g., User Management)"
                        value={module.dependencies || ""}
                        onChange={(e) =>
                          updateFunctionalModule(
                            index,
                            "dependencies",
                            e.target.value
                          )
                        }
                        className="rounded-lg"
                      />
                    </Form.Item>
                  </Col>
                </Row>

                {/* Priority Badge */}
                <div className="mt-3 flex justify-between items-center">
                  <Tag
                    color={getPriorityColor(module.priority)}
                    className="text-sm"
                  >
                    {module.priority} Priority
                  </Tag>
                  <span className="text-xs text-gray-500">
                    Module #{index + 1}
                  </span>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Module Guidelines */}
        <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
          <Title level={5} className="text-green-800 mb-2">
            Module Definition Guidelines
          </Title>
          <ul className="text-sm text-green-700 space-y-1">
            <li>
              • Break down your application into logical, independent modules
            </li>
            <li>• Each module should have a clear, single responsibility</li>
            <li>• Consider user workflows and business processes</li>
            <li>
              • Set priorities based on business value and technical
              dependencies
            </li>
          </ul>
        </div>

        {/* Quick Add Common Modules */}
        <div className="mt-4">
          <Title level={5} className="mb-3">
            Quick Add Common Modules
          </Title>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            {modulesSuggestions.map((moduleName) => (
              <Button
                key={moduleName}
                size="small"
                onClick={() => {
                  addFunctionalModule();
                  const newIndex = functionalModules.length;
                  setTimeout(() => {
                    updateFunctionalModule(newIndex, "description", moduleName);
                  }, 100);
                }}
                className="text-left text-blue-600 border-blue-300 hover:bg-blue-50 h-auto py-2 px-3"
              >
                <div className="text-xs">+ {moduleName}</div>
              </Button>
            ))}
          </div>
        </div>

        {/* Module Summary */}
        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="font-medium text-gray-700">
              Total Modules: {functionalModules.length}
            </span>
            <div className="flex space-x-4 text-sm">
              <span className="text-red-600">
                High:{" "}
                {functionalModules.filter((m) => m.priority === "High").length}
              </span>
              <span className="text-orange-600">
                Medium:{" "}
                {
                  functionalModules.filter((m) => m.priority === "Medium")
                    .length
                }
              </span>
              <span className="text-green-600">
                Low:{" "}
                {functionalModules.filter((m) => m.priority === "Low").length}
              </span>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

export default Step5FunctionalModules;
