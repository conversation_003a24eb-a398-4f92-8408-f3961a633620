import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for monitoring network connectivity
 * @returns {Object} Network status and utilities
 */
export const useNetwork = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionType, setConnectionType] = useState('unknown');
  const [effectiveType, setEffectiveType] = useState('unknown');
  const [downlink, setDownlink] = useState(0);
  const [rtt, setRtt] = useState(0);
  const [saveData, setSaveData] = useState(false);
  const [lastOnlineTime, setLastOnlineTime] = useState(Date.now());
  const [lastOfflineTime, setLastOfflineTime] = useState(null);

  // Update network information
  const updateNetworkInfo = useCallback(() => {
    const connection = navigator.connection || 
                      navigator.mozConnection || 
                      navigator.webkitConnection;
    
    if (connection) {
      setConnectionType(connection.type || 'unknown');
      setEffectiveType(connection.effectiveType || 'unknown');
      setDownlink(connection.downlink || 0);
      setRtt(connection.rtt || 0);
      setSaveData(connection.saveData || false);
    }
  }, []);

  // Handle online event
  const handleOnline = useCallback(() => {
    setIsOnline(true);
    setLastOnlineTime(Date.now());
    updateNetworkInfo();
  }, [updateNetworkInfo]);

  // Handle offline event
  const handleOffline = useCallback(() => {
    setIsOnline(false);
    setLastOfflineTime(Date.now());
  }, []);

  // Handle connection change
  const handleConnectionChange = useCallback(() => {
    updateNetworkInfo();
  }, [updateNetworkInfo]);

  // Setup event listeners
  useEffect(() => {
    // Initial network info update
    updateNetworkInfo();

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    const connection = navigator.connection || 
                      navigator.mozConnection || 
                      navigator.webkitConnection;
    
    if (connection) {
      connection.addEventListener('change', handleConnectionChange);
    }

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (connection) {
        connection.removeEventListener('change', handleConnectionChange);
      }
    };
  }, [handleOnline, handleOffline, handleConnectionChange, updateNetworkInfo]);

  // Ping test to verify actual connectivity
  const pingTest = useCallback(async (url = 'https://www.google.com/favicon.ico') => {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      });
      return true;
    } catch {
      return false;
    }
  }, []);

  // Get connection quality
  const getConnectionQuality = useCallback(() => {
    if (!isOnline) return 'offline';
    
    if (effectiveType === '4g' && downlink > 10) return 'excellent';
    if (effectiveType === '4g' || (effectiveType === '3g' && downlink > 1.5)) return 'good';
    if (effectiveType === '3g' || effectiveType === '2g') return 'poor';
    
    return 'unknown';
  }, [isOnline, effectiveType, downlink]);

  // Get offline duration
  const getOfflineDuration = useCallback(() => {
    if (isOnline || !lastOfflineTime) return 0;
    return Date.now() - lastOfflineTime;
  }, [isOnline, lastOfflineTime]);

  // Get online duration
  const getOnlineDuration = useCallback(() => {
    if (!isOnline) return 0;
    return Date.now() - lastOnlineTime;
  }, [isOnline, lastOnlineTime]);

  return {
    isOnline,
    connectionType,
    effectiveType,
    downlink,
    rtt,
    saveData,
    lastOnlineTime,
    lastOfflineTime,
    connectionQuality: getConnectionQuality(),
    offlineDuration: getOfflineDuration(),
    onlineDuration: getOnlineDuration(),
    pingTest,
    isSlowConnection: effectiveType === '2g' || effectiveType === 'slow-2g',
    isFastConnection: effectiveType === '4g' && downlink > 10,
    isSupported: 'onLine' in navigator
  };
};

/**
 * Hook for managing offline queue
 * @returns {Object} Offline queue management
 */
export const useOfflineQueue = () => {
  const [queue, setQueue] = useState([]);
  const { isOnline } = useNetwork();

  // Add item to queue
  const addToQueue = useCallback((item) => {
    const queueItem = {
      id: `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      ...item
    };
    
    setQueue(prev => [...prev, queueItem]);
    return queueItem.id;
  }, []);

  // Remove item from queue
  const removeFromQueue = useCallback((id) => {
    setQueue(prev => prev.filter(item => item.id !== id));
  }, []);

  // Clear queue
  const clearQueue = useCallback(() => {
    setQueue([]);
  }, []);

  // Process queue when online
  useEffect(() => {
    if (isOnline && queue.length > 0) {
      // Process queue items
      queue.forEach(async (item) => {
        try {
          if (item.action && typeof item.action === 'function') {
            await item.action(item.data);
            removeFromQueue(item.id);
          }
        } catch (error) {
          console.error('Error processing queue item:', error);
          // Keep item in queue for retry
        }
      });
    }
  }, [isOnline, queue, removeFromQueue]);

  return {
    queue,
    addToQueue,
    removeFromQueue,
    clearQueue,
    queueSize: queue.length,
    hasQueuedItems: queue.length > 0
  };
};

/**
 * Hook for network-aware data fetching
 * @param {Function} fetchFunction - Function to fetch data
 * @param {Object} options - Options for fetching
 * @returns {Object} Fetch state and utilities
 */
export const useNetworkAwareFetch = (fetchFunction, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { isOnline, connectionQuality } = useNetwork();
  const { addToQueue } = useOfflineQueue();

  const {
    retryOnReconnect = true,
    cacheData = true,
    offlineMessage = 'This action requires an internet connection'
  } = options;

  // Fetch data
  const fetchData = useCallback(async (...args) => {
    if (!isOnline) {
      if (retryOnReconnect) {
        addToQueue({
          action: () => fetchData(...args),
          data: args,
          type: 'fetch'
        });
      }
      setError(new Error(offlineMessage));
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const result = await fetchFunction(...args);
      setData(result);
      
      if (cacheData) {
        localStorage.setItem(
          `cache_${fetchFunction.name}`,
          JSON.stringify({
            data: result,
            timestamp: Date.now(),
            args
          })
        );
      }
      
      return result;
    } catch (err) {
      setError(err);
      
      // Try to load from cache if available
      if (cacheData) {
        try {
          const cached = localStorage.getItem(`cache_${fetchFunction.name}`);
          if (cached) {
            const { data: cachedData } = JSON.parse(cached);
            setData(cachedData);
          }
        } catch (cacheError) {
          console.error('Error loading cached data:', cacheError);
        }
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [isOnline, fetchFunction, retryOnReconnect, addToQueue, offlineMessage, cacheData]);

  return {
    data,
    loading,
    error,
    fetchData,
    isOnline,
    connectionQuality,
    canFetch: isOnline
  };
};
