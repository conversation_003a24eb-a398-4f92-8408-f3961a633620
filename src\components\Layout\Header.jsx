import { Link, useLocation } from "react-router-dom";
import { Layout, <PERSON>u, Button } from "antd";
import { FileText, Home, Info, Mail, History, Phone } from "lucide-react";
import { motion } from "framer-motion";
import { useNavigationGuard } from "../../hooks/useNavigationGuard";
import SaveProgressModal from "../SRS/SaveProgressModal";

const { Header: AntHeader } = Layout;

const Header = () => {
  const location = useLocation();
  const {
    guardedNavigate,
    showSaveProgressModal,
    isSavingProgress,
    getInProgressInfo,
    handleSaveAndNavigate,
    handleContinueCurrent,
    handleDiscardAndNavigate,
    handleModalClose,
  } = useNavigationGuard();

  // Handle navigation with guard
  const handleNavigation = (path) => {
    guardedNavigate(path);
  };

  const menuItems = [
    {
      key: "/",
      icon: <Home size={16} />,
      label: <Link to="/">Home</Link>,
    },
    {
      key: "/generate",
      icon: <FileText size={16} />,
      label: (
        <span
          onClick={(e) => {
            e.preventDefault();
            handleNavigation("/generate");
          }}
        >
          Generate SRS
        </span>
      ),
    },
    {
      key: "/history",
      icon: <History size={16} />,
      label: <Link to="/history">History</Link>,
    },
    {
      key: "/about",
      icon: <Info size={16} />,
      label: <Link to="/about">About</Link>,
    },
    {
      key: "/contact",
      icon: <Mail size={16} />,
      label: <Link to="/contact">Contact</Link>,
    },
  ];

  return (
    <AntHeader className="bg-white shadow-sm border-b border-gray-100 px-4 lg:px-8">
      <div className="flex items-center justify-between h-full max-w-7xl mx-auto">
        {/* Logo */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="flex items-center space-x-3"
        >
          <div className="w-10 h-10 bg-gradient-to-r from-primary-600 to-electric-500 rounded-lg flex items-center justify-center">
            <FileText className="text-white" size={20} />
          </div>
          <div className="hidden sm:block">
            <h1 className="text-xl font-bold gradient-text">SRS Generator</h1>
            <p className="text-xs text-gray-500">AI-Powered Documentation</p>
          </div>
        </motion.div>

        {/* Navigation Menu */}
        <div className="flex-1 flex justify-center">
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            className="border-none bg-transparent min-w-0 flex-1 justify-center max-w-2xl"
            style={{ lineHeight: "64px" }}
          />
        </div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="hidden md:block"
        >
          <Button
            type="primary"
            size="large"
            className="btn-primary"
            icon={<FileText size={16} />}
            onClick={() => handleNavigation("/generate")}
          >
            Create SRS
          </Button>
        </motion.div>
      </div>

      {/* Save Progress Modal */}
      {showSaveProgressModal && (
        <SaveProgressModal
          visible={showSaveProgressModal}
          onCancel={handleModalClose}
          onSaveAndStartNew={handleSaveAndNavigate}
          onContinueCurrent={handleContinueCurrent}
          onDiscardAndStartNew={handleDiscardAndNavigate}
          currentStep={getInProgressInfo()?.currentStep || 0}
          totalSteps={getInProgressInfo()?.totalSteps || 8}
          projectName={getInProgressInfo()?.projectName || "Untitled Project"}
          loading={isSavingProgress}
        />
      )}
    </AntHeader>
  );
};

export default Header;
