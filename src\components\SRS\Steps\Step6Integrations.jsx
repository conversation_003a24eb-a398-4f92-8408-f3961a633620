import { useState, useRef } from "react";
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  Typography,
  Button,
  Space,
  Divider,
} from "antd";
import { Shield, Plus } from "lucide-react";
import { motion } from "framer-motion";
import { INTEGRATION_OPTIONS } from "../../../constants/srsFormData";

const { Title } = Typography;
const { TextArea } = Input;

const Step6Integrations = ({ form }) => {
  const [integrationOptions, setIntegrationOptions] = useState({
    payment: [...INTEGRATION_OPTIONS.payment],
    authentication: [...INTEGRATION_OPTIONS.authentication],
    maps: [...INTEGRATION_OPTIONS.maps],
    analytics: [...INTEGRATION_OPTIONS.analytics],
    email: [...INTEGRATION_OPTIONS.email],
    storage: [...INTEGRATION_OPTIONS.storage],
    social: [...INTEGRATION_OPTIONS.social],
    communication: [...INTEGRATION_OPTIONS.communication],
    search: [...INTEGRATION_OPTIONS.search],
    cms: [...INTEGRATION_OPTIONS.cms],
  });

  const [newIntegration, setNewIntegration] = useState({
    payment: "",
    authentication: "",
    maps: "",
    analytics: "",
    email: "",
    storage: "",
    social: "",
    communication: "",
    search: "",
    cms: "",
  });

  const inputRefs = {
    payment: useRef(null),
    authentication: useRef(null),
    maps: useRef(null),
    analytics: useRef(null),
    email: useRef(null),
    storage: useRef(null),
    social: useRef(null),
    communication: useRef(null),
    search: useRef(null),
    cms: useRef(null),
  };

  // Add custom integration to a category
  const addCustomIntegration = (category, e) => {
    e.preventDefault();
    const value = newIntegration[category]?.trim();
    if (value && !integrationOptions[category].includes(value)) {
      setIntegrationOptions((prev) => ({
        ...prev,
        [category]: [...prev[category], value],
      }));
      setNewIntegration((prev) => ({ ...prev, [category]: "" }));
      setTimeout(() => {
        inputRefs[category].current?.focus();
      }, 0);
    }
  };

  const integrationCategories = [
    {
      key: "payment",
      label: "Payment Processing",
      icon: "💳",
      description: "Payment gateways and billing systems",
    },
    {
      key: "authentication",
      label: "Authentication",
      icon: "🔐",
      description: "User authentication and authorization services",
    },
    {
      key: "maps",
      label: "Maps & Location",
      icon: "🗺️",
      description: "Mapping and geolocation services",
    },
    {
      key: "analytics",
      label: "Analytics",
      icon: "📊",
      description: "User behavior and performance analytics",
    },
    {
      key: "email",
      label: "Email Services",
      icon: "📧",
      description: "Email delivery and marketing platforms",
    },
    {
      key: "storage",
      label: "Cloud Storage",
      icon: "☁️",
      description: "File storage and CDN services",
    },
    {
      key: "social",
      label: "Social Media",
      icon: "📱",
      description: "Social media platform integrations",
    },
    {
      key: "communication",
      label: "Communication",
      icon: "💬",
      description: "Messaging, chat, and notification services",
    },
    {
      key: "search",
      label: "Search",
      icon: "🔍",
      description: "Search engines and indexing services",
    },
    {
      key: "cms",
      label: "Content Management",
      icon: "📝",
      description: "Headless CMS and content platforms",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <Shield className="mr-3 text-blue-600" size={24} />
          Third-Party Integrations
        </Title>

        <Row gutter={[24, 24]}>
          {integrationCategories.map(({ key, label, icon, description }) => (
            <Col xs={24} md={12} key={key}>
              <div className="border border-gray-200 rounded-lg p-4 h-full">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-2xl">{icon}</span>
                  <div>
                    <Title level={5} className="mb-0">
                      {label}
                    </Title>
                    <p className="text-xs text-gray-500 mb-0">{description}</p>
                  </div>
                </div>

                <Form.Item name={`integrations_${key}`} className="mb-0">
                  <Select
                    mode="multiple"
                    placeholder={`Select ${label.toLowerCase()}`}
                    size="large"
                    className="w-full"
                    value={form.getFieldValue(`integrations_${key}`)}
                    onChange={(value) => {
                      form.setFieldValue(`integrations_${key}`, value);
                      console.log(
                        `📝 Step6: integrations_${key} updated:`,
                        value
                      );
                    }}
                    filterOption={(input, option) =>
                      option.label.toLowerCase().includes(input.toLowerCase())
                    }
                    popupRender={(menu) => (
                      <>
                        {menu}
                        <Divider style={{ margin: "8px 0" }} />
                        <Space style={{ padding: "0 8px 4px" }}>
                          <Input
                            placeholder={`Add custom ${label.toLowerCase()}`}
                            ref={inputRefs[key]}
                            value={newIntegration[key]}
                            onChange={(e) =>
                              setNewIntegration((prev) => ({
                                ...prev,
                                [key]: e.target.value,
                              }))
                            }
                            onKeyDown={(e) => {
                              e.stopPropagation();
                              if (e.key === "Enter") {
                                addCustomIntegration(key, e);
                              }
                            }}
                            style={{ width: 160 }}
                          />
                          <Button
                            type="text"
                            icon={<Plus size={14} />}
                            onClick={(e) => addCustomIntegration(key, e)}
                            disabled={!newIntegration[key]?.trim()}
                          >
                            Add
                          </Button>
                        </Space>
                      </>
                    )}
                    options={integrationOptions[key].map((option) => ({
                      label: option,
                      value: option,
                    }))}
                  />
                </Form.Item>
              </div>
            </Col>
          ))}
        </Row>

        <Divider />

        {/* Custom Integrations Section */}
        <Row gutter={[24, 24]}>
          <Col xs={24}>
            <Title level={4} className="mb-4">
              Additional Integration Requirements
            </Title>

            <Form.Item
              name="customIntegrations"
              label="Custom Integrations & APIs"
            >
              <TextArea
                rows={4}
                placeholder="Describe any custom integrations, APIs, or third-party services not listed above..."
                className="rounded-lg"
                value={form.getFieldValue("customIntegrations")}
                onChange={(e) => {
                  form.setFieldValue("customIntegrations", e.target.value);
                  console.log(
                    "📝 Step6: customIntegrations updated:",
                    e.target.value
                  );
                }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="integrationBudget"
              label="Integration Budget Considerations"
            >
              <Select
                placeholder="Select budget range for integrations"
                size="large"
                value={form.getFieldValue("integrationBudget")}
                onChange={(value) => {
                  form.setFieldValue("integrationBudget", value);
                  console.log("📝 Step6: integrationBudget updated:", value);
                }}
                options={[
                  { label: "Free/Open Source Only", value: "free" },
                  { label: "Low Budget ($0-$100/month)", value: "low" },
                  { label: "Medium Budget ($100-$500/month)", value: "medium" },
                  { label: "High Budget ($500+/month)", value: "high" },
                  { label: "Enterprise Level", value: "enterprise" },
                  { label: "Not Decided", value: "undecided" },
                ]}
              />
            </Form.Item>
          </Col>

          <Col xs={24} md={12}>
            <Form.Item
              name="integrationComplexity"
              label="Integration Complexity Preference"
            >
              <Select
                placeholder="Select complexity preference"
                size="large"
                value={form.getFieldValue("integrationComplexity")}
                onChange={(value) => {
                  form.setFieldValue("integrationComplexity", value);
                  console.log(
                    "📝 Step6: integrationComplexity updated:",
                    value
                  );
                }}
                options={[
                  { label: "Simple (Plug & Play)", value: "simple" },
                  { label: "Moderate (Some Configuration)", value: "moderate" },
                  { label: "Complex (Custom Development)", value: "complex" },
                  { label: "Mixed (Depends on Service)", value: "mixed" },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Integration Guidelines */}
        <div className="mt-6 p-4 bg-purple-50 rounded-lg border border-purple-200">
          <Title level={5} className="text-purple-800 mb-2">
            Integration Selection Guidelines
          </Title>
          <ul className="text-sm text-purple-700 space-y-1">
            <li>
              • Choose integrations that align with your budget and technical
              requirements
            </li>
            <li>
              • Consider data privacy and security implications of each service
            </li>
            <li>
              • Evaluate the long-term costs and scalability of selected
              services
            </li>
            <li>
              • Ensure integrations support your target regions and compliance
              needs
            </li>
          </ul>
        </div>
      </Card>
    </motion.div>
  );
};

export default Step6Integrations;
