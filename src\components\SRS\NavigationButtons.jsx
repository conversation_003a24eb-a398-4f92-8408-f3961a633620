import { <PERSON>, <PERSON>ton, Progress, Typography } from "antd";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON>, Save, Rocket } from "lucide-react";
import { motion } from "framer-motion";

const { Text } = Typography;

// Sanitize data to remove circular references and React elements (MOVED OUTSIDE COMPONENT)
const sanitizeData = (data) => {
  if (data === null || data === undefined) {
    return data;
  }

  // Handle primitive types
  if (typeof data !== "object") {
    return data;
  }

  // Handle arrays
  if (Array.isArray(data)) {
    return data.map((item) => sanitizeData(item));
  }

  // Handle objects
  const sanitized = {};
  for (const [key, value] of Object.entries(data)) {
    // Skip React-specific properties and DOM elements
    if (
      key.startsWith("__react") ||
      key.startsWith("_react") ||
      key === "nativeEvent" ||
      key === "currentTarget" ||
      key === "target" ||
      (value &&
        typeof value === "object" &&
        value.constructor &&
        (value.constructor.name.includes("HTML") ||
          value.constructor.name.includes("Element") ||
          value.constructor.name.includes("Node")))
    ) {
      continue;
    }

    // Recursively sanitize nested objects
    try {
      sanitized[key] = sanitizeData(value);
    } catch {
      // Skip properties that cause circular references
      console.warn(`Skipping property ${key} due to circular reference`);
      continue;
    }
  }

  return sanitized;
};

const NavigationButtons = ({
  currentStep,
  totalSteps,
  isGenerating,
  generationProgress,
  generationStep,
  onPrevious,
  onNext,
  onGenerate,
  onSave,
  onSaveProgress, // New prop for saving progress
  getAllFormData, // New prop for getting complete form data
  form,
  userRoles,
  functionalModules,
  teamMembers,
  selectedPlatforms,
  aiConfigured = true,
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  const handleSave = () => {
    console.log("💾 Save Progress button clicked");

    try {
      // Use centralized function to get ALL form data from all steps
      const completeFormData = getAllFormData
        ? getAllFormData()
        : form.getFieldsValue();

      const progressData = {
        formData: completeFormData,
        currentStep,
        userRoles,
        functionalModules,
        teamMembers,
        selectedPlatforms,
        savedAt: new Date().toISOString(),
      };

      // Sanitize data before processing
      const sanitizedProgressData = sanitizeData(progressData);
      console.log("📊 Complete progress data to save:", sanitizedProgressData);

      // Use new save progress functionality if available, otherwise fallback to old method
      if (onSaveProgress) {
        console.log("✅ Using onSaveProgress function");
        onSaveProgress(sanitizedProgressData);

        // ALSO update localStorage for consistency (in case onSaveProgress doesn't do it)
        try {
          localStorage.setItem(
            "srs_form_data",
            JSON.stringify(sanitizedProgressData)
          );
          console.log("✅ Also updated srs_form_data for consistency");
        } catch (error) {
          console.warn("⚠️ Failed to update srs_form_data:", error);
        }
      } else {
        console.log("⚠️ Fallback to localStorage save");
        localStorage.setItem(
          "srs_form_data",
          JSON.stringify(sanitizedProgressData)
        );
        if (onSave) {
          onSave();
        }
      }
    } catch (error) {
      console.error("❌ Save progress failed:", error);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card bg-white shadow-lg border-gray-200">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Previous Button */}
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              size="large"
              icon={<ArrowLeft size={16} />}
              onClick={onPrevious}
              disabled={isFirstStep || isGenerating}
              className={`btn-secondary min-w-[120px] ${
                isFirstStep
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:shadow-md"
              }`}
            >
              Previous
            </Button>
          </motion.div>

          {/* Center Actions */}
          <div className="flex items-center space-x-4">
            {/* Save Progress Button */}
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                size="large"
                icon={<Save size={16} />}
                onClick={handleSave}
                disabled={isGenerating}
                className="btn-secondary hover:shadow-md"
              >
                <span className="hidden md:inline">Save Progress</span>
                <span className="md:hidden">Save</span>
              </Button>
            </motion.div>

            {/* Step Indicator for Mobile */}
            <div className="md:hidden text-center">
              <div className="text-sm font-medium text-gray-700">
                Step {currentStep + 1} of {totalSteps}
              </div>
              <div className="text-xs text-gray-500">
                {Math.round(((currentStep + 1) / totalSteps) * 100)}% Complete
              </div>
            </div>
          </div>

          {/* Next/Generate Button */}
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            {isLastStep ? (
              <Button
                type="primary"
                size="large"
                icon={<Rocket size={16} />}
                onClick={onGenerate || onNext}
                loading={isGenerating}
                disabled={isGenerating || !aiConfigured}
                className="btn-primary min-w-[180px] bg-gradient-to-r from-blue-600 to-cyan-600 border-none hover:from-blue-700 hover:to-cyan-700 shadow-lg"
              >
                {isGenerating ? (
                  <span>{generationStep || "Generating SRS..."}</span>
                ) : !aiConfigured ? (
                  <span>AI Not Configured</span>
                ) : (
                  <span>Generate SRS Document</span>
                )}
              </Button>
            ) : (
              <Button
                type="primary"
                size="large"
                icon={<ArrowRight size={16} />}
                onClick={onNext}
                disabled={isGenerating}
                className="btn-primary min-w-[120px] hover:shadow-md"
              >
                Next Step
              </Button>
            )}
          </motion.div>
        </div>

        {/* Generation Progress Section */}
        {isGenerating && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-6 pt-6 border-t border-gray-200"
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                  >
                    <Rocket className="text-blue-600" size={20} />
                  </motion.div>
                  <div>
                    <Text className="font-medium text-gray-800">
                      {generationStep || "Generating your SRS document..."}
                    </Text>
                    <div className="text-sm text-gray-500">
                      AI is analyzing your requirements and creating a
                      professional document
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">
                    {generationProgress}%
                  </div>
                  <div className="text-xs text-gray-500">Complete</div>
                </div>
              </div>

              {/* Enhanced Progress Bar */}
              <div className="space-y-2">
                <Progress
                  percent={generationProgress}
                  status="active"
                  strokeColor={{
                    "0%": "#3b82f6",
                    "50%": "#06b6d4",
                    "100%": "#10b981",
                  }}
                  trailColor="#f3f4f6"
                  strokeWidth={8}
                  className="[&_.ant-progress-text]:hidden"
                />

                {/* Generation Steps */}
                <div className="flex justify-between text-xs text-gray-500">
                  <span
                    className={
                      generationProgress >= 20
                        ? "text-blue-600 font-medium"
                        : ""
                    }
                  >
                    Analyzing Requirements
                  </span>
                  <span
                    className={
                      generationProgress >= 40
                        ? "text-blue-600 font-medium"
                        : ""
                    }
                  >
                    Structuring Document
                  </span>
                  <span
                    className={
                      generationProgress >= 60
                        ? "text-blue-600 font-medium"
                        : ""
                    }
                  >
                    Generating Content
                  </span>
                  <span
                    className={
                      generationProgress >= 80
                        ? "text-blue-600 font-medium"
                        : ""
                    }
                  >
                    Formatting
                  </span>
                  <span
                    className={
                      generationProgress >= 100
                        ? "text-green-600 font-medium"
                        : ""
                    }
                  >
                    Complete
                  </span>
                </div>
              </div>

              {/* Estimated Time */}
              <div className="text-center">
                <Text className="text-sm text-gray-600">
                  Estimated time remaining:{" "}
                  {Math.max(0, Math.ceil((100 - generationProgress) / 20))}{" "}
                  seconds
                </Text>
              </div>
            </div>
          </motion.div>
        )}

        {/* Tips Section */}
        {!isGenerating && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-4 pt-4 border-t border-gray-100"
          >
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-2 md:space-y-0">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <Text className="text-sm text-gray-600">
                  {isLastStep
                    ? "Review your information before generating the SRS document"
                    : "Fill in all required fields to proceed to the next step"}
                </Text>
              </div>

              <div className="text-xs text-gray-400">
                Press Tab to navigate between fields
              </div>
            </div>
          </motion.div>
        )}
      </Card>
    </motion.div>
  );
};

export default NavigationButtons;
