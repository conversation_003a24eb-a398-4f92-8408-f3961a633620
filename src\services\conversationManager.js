class ConversationManager {
  constructor() {
    this.conversations = new Map();
    this.storageType = 'localStorage'; // Future: 'backend'
  }

  // Initialize new SRS project conversation
  initializeProject(projectId, formData) {
    const projectData = {
      id: projectId,
      projectName: formData.projectName || 'Untitled Project',
      formData: formData,
      conversationSteps: {
        step1: null,
        step2: null,
        step3: null
      },
      conversationHistory: [
        {
          role: 'system',
          content: `You are an SRS Creation Agent following the exact ChatGPT methodology for generating detailed SRS documents.

🎯 Purpose: To generate a detailed and complete SRS document in a multi-step process. You must not skip any steps and must not summarize any section.

DEFAULT TECH STACK:
- Frontend: React.js (Admin Dashboard)
- Mobile App: Flutter (Cross-platform: Android & iOS)  
- Backend: Node.js (Express.js)
- Database: PostgreSQL (multi-tenant schema)
- Cache Management: Redis
- Media Storage: AWS S3 / Object Storage
- Hosting: AWS / Azure / GCP
- CI/CD: Standard CI-CD

CRITICAL INSTRUCTIONS:
1. Maintain conversation continuity across all steps
2. Remember all previous exchanges and context
3. Build upon previous information without repeating questions
4. Generate project-specific content, not generic templates`
        },
        {
          role: 'user',
          content: `New SRS Project Data: ${JSON.stringify(formData, null, 2)}`
        }
      ],
      status: 'active',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      totalTokens: 0,
      totalCost: 0,
      editCount: 0
    };

    this.conversations.set(projectId, projectData);
    this.saveToStorage(projectId, projectData);
    return projectData;
  }

  // Add message to conversation
  addMessage(projectId, role, content, stepNumber = null, tokens = 0, cost = 0) {
    const project = this.conversations.get(projectId);
    if (!project) return null;

    const message = {
      role,
      content,
      timestamp: new Date().toISOString(),
      tokens,
      cost
    };

    // Add to conversation history
    project.conversationHistory.push(message);

    // Store step-specific data
    if (stepNumber) {
      project.conversationSteps[`step${stepNumber}`] = {
        request: role === 'user' ? content : project.conversationSteps[`step${stepNumber}`]?.request,
        response: role === 'assistant' ? content : project.conversationSteps[`step${stepNumber}`]?.response,
        timestamp: new Date().toISOString(),
        tokens,
        cost
      };
    }

    // Update totals
    project.totalTokens += tokens;
    project.totalCost += cost;
    project.lastModified = new Date().toISOString();

    this.conversations.set(projectId, project);
    this.saveToStorage(projectId, project);

    return message;
  }

  // Get full conversation for editing
  getFullConversation(projectId) {
    const project = this.conversations.get(projectId);
    return project ? project.conversationHistory : [];
  }

  // Get conversation summary
  getConversationSummary(projectId) {
    const project = this.conversations.get(projectId);
    if (!project) return null;

    return {
      projectName: project.projectName,
      totalExchanges: project.conversationHistory.length,
      estimatedTokens: project.totalTokens,
      totalCost: project.totalCost,
      lastModified: project.lastModified,
      status: project.status,
      editCount: project.editCount
    };
  }

  // Get project data
  getProject(projectId) {
    let project = this.conversations.get(projectId);

    // If not in memory, try to load from storage
    if (!project) {
      project = this.loadFromStorage(projectId);
      if (project) {
        this.conversations.set(projectId, project);
      }
    }

    return project;
  }

  // Mark project as completed
  markProjectCompleted(projectId, generatedSRS) {
    const project = this.conversations.get(projectId);
    if (project) {
      project.status = 'completed';
      project.generatedSRS = generatedSRS;
      project.lastModified = new Date().toISOString();
      this.conversations.set(projectId, project);
      this.saveToStorage(projectId, project);
    }
  }

  // Increment edit count
  incrementEditCount(projectId) {
    const project = this.conversations.get(projectId);
    if (project) {
      project.editCount += 1;
      project.lastModified = new Date().toISOString();
      this.conversations.set(projectId, project);
      this.saveToStorage(projectId, project);
    }
  }

  // Smart context management for long conversations
  // OPTIMIZED to prevent memory issues and token overflow
  getOptimizedConversation(projectId, maxTokens = 6000) {
    const conversation = this.getFullConversation(projectId);

    if (!conversation || conversation.length === 0) {
      return [];
    }

    const estimatedTokens = this.estimateTokens(conversation);

    if (estimatedTokens <= maxTokens) {
      return conversation;
    }

    // Keep system prompt + recent exchanges (reduced from 10 to 6 for memory efficiency)
    const systemPrompt = conversation[0];
    const recentMessages = conversation.slice(-6); // Last 6 exchanges only

    // Add conversation summary (more concise)
    const summary = {
      role: 'assistant',
      content: `[Context: SRS generation in progress. Previous steps completed project analysis and requirement gathering.]`,
      timestamp: new Date().toISOString()
    };

    const optimizedConversation = [systemPrompt, summary, ...recentMessages];

    // Final check - if still too large, keep only system prompt + last 3 messages
    if (this.estimateTokens(optimizedConversation) > maxTokens) {
      return [systemPrompt, ...conversation.slice(-3)];
    }

    return optimizedConversation;
  }

  // Estimate tokens (rough calculation)
  estimateTokens(conversation) {
    return conversation.reduce((total, message) => {
      return total + Math.ceil(message.content.length / 4);
    }, 0);
  }

  // Storage methods
  // OPTIMIZED to prevent localStorage overflow
  saveToStorage(projectId, projectData) {
    if (this.storageType === 'localStorage') {
      try {
        // Create a lightweight version for storage
        const lightweightData = {
          id: projectData.id,
          projectName: projectData.projectName,
          status: projectData.status,
          createdAt: projectData.createdAt,
          lastModified: projectData.lastModified,
          totalTokens: projectData.totalTokens,
          totalCost: projectData.totalCost,
          editCount: projectData.editCount,
          // Only store essential conversation data
          conversationSteps: projectData.conversationSteps,
          // Limit conversation history to last 10 messages to prevent overflow
          conversationHistory: projectData.conversationHistory.slice(-10),
          // Store generated SRS if completed
          generatedSRS: projectData.status === 'completed' ? projectData.generatedSRS : undefined
        };

        const dataString = JSON.stringify(lightweightData);

        // Check localStorage size limit (5MB typical limit)
        if (dataString.length > 4 * 1024 * 1024) { // 4MB limit for safety
          console.warn('⚠️ Conversation data too large, storing minimal version');
          const minimalData = {
            id: projectData.id,
            projectName: projectData.projectName,
            status: projectData.status,
            lastModified: projectData.lastModified,
            totalCost: projectData.totalCost
          };
          localStorage.setItem(`srs_conversation_${projectId}`, JSON.stringify(minimalData));
        } else {
          localStorage.setItem(`srs_conversation_${projectId}`, dataString);
        }
      } catch (error) {
        console.error('Failed to save conversation to localStorage:', error);
        // Try to save minimal data as fallback
        try {
          const minimalData = {
            id: projectId,
            projectName: projectData.projectName || 'Untitled',
            status: projectData.status || 'active',
            lastModified: new Date().toISOString()
          };
          localStorage.setItem(`srs_conversation_${projectId}`, JSON.stringify(minimalData));
        } catch (fallbackError) {
          console.error('Even minimal save failed:', fallbackError);
        }
      }
    }
    // Future: backend storage implementation
  }

  loadFromStorage(projectId) {
    if (this.storageType === 'localStorage') {
      try {
        const data = localStorage.getItem(`srs_conversation_${projectId}`);
        return data ? JSON.parse(data) : null;
      } catch (error) {
        console.error('Failed to load conversation from localStorage:', error);
        return null;
      }
    }
    // Future: backend storage implementation
    return null;
  }

  // Get all stored conversations
  getAllConversations() {
    const conversations = [];

    if (this.storageType === 'localStorage') {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('srs_conversation_')) {
          try {
            const data = JSON.parse(localStorage.getItem(key));
            conversations.push(data);
          } catch (error) {
            console.error('Failed to parse conversation data:', error);
          }
        }
      }
    }

    return conversations.sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));
  }

  // Delete conversation
  deleteConversation(projectId) {
    this.conversations.delete(projectId);

    if (this.storageType === 'localStorage') {
      localStorage.removeItem(`srs_conversation_${projectId}`);
    }
  }

  // Get cost statistics
  getCostStatistics() {
    const conversations = this.getAllConversations();

    const stats = {
      totalProjects: conversations.length,
      totalCost: 0,
      totalTokens: 0,
      totalEdits: 0,
      thisMonth: 0,
      avgCostPerProject: 0
    };

    const thisMonth = new Date().getMonth();
    const thisYear = new Date().getFullYear();

    conversations.forEach(conv => {
      stats.totalCost += conv.totalCost || 0;
      stats.totalTokens += conv.totalTokens || 0;
      stats.totalEdits += conv.editCount || 0;

      const convDate = new Date(conv.createdAt);
      if (convDate.getMonth() === thisMonth && convDate.getFullYear() === thisYear) {
        stats.thisMonth += conv.totalCost || 0;
      }
    });

    stats.avgCostPerProject = stats.totalProjects > 0 ? stats.totalCost / stats.totalProjects : 0;

    return stats;
  }

  // MEMORY CLEANUP METHODS
  // Clean up old conversations to prevent memory issues
  cleanupOldConversations(maxAge = 30) { // 30 days default
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - maxAge);

    let cleanedCount = 0;

    if (this.storageType === 'localStorage') {
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key && key.startsWith('srs_conversation_')) {
          try {
            const data = JSON.parse(localStorage.getItem(key));
            const lastModified = new Date(data.lastModified);

            if (lastModified < cutoffDate && data.status !== 'completed') {
              localStorage.removeItem(key);
              cleanedCount++;
            }
          } catch (error) {
            // Remove corrupted entries
            localStorage.removeItem(key);
            cleanedCount++;
          }
        }
      }
    }

    // Clear in-memory conversations
    this.conversations.clear();

    console.log(`🧹 Cleaned up ${cleanedCount} old conversations`);
    return cleanedCount;
  }

  // Force cleanup of memory
  forceMemoryCleanup() {
    // Clear in-memory conversations
    this.conversations.clear();

    // Run garbage collection if available
    if (window.gc) {
      window.gc();
    }

    console.log('🧹 Forced memory cleanup completed');
  }
}

// Create singleton instance
const conversationManager = new ConversationManager();

export default conversationManager;
