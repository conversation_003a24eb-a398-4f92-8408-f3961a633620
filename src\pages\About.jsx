import React from 'react';
import { <PERSON>, <PERSON>, Card, Timeline, Avatar } from 'antd';
import { FileText, Target, Users, Award, Lightbulb, Rocket } from 'lucide-react';
import { motion } from 'framer-motion';

const About = () => {
  const values = [
    {
      icon: <Lightbulb className="text-yellow-500" size={32} />,
      title: 'Innovation',
      description: 'We continuously push the boundaries of AI technology to deliver cutting-edge solutions.',
    },
    {
      icon: <Users className="text-blue-500" size={32} />,
      title: 'User-Centric',
      description: 'Every feature is designed with our users in mind, ensuring intuitive and efficient workflows.',
    },
    {
      icon: <Award className="text-purple-500" size={32} />,
      title: 'Quality',
      description: 'We maintain the highest standards in document generation and software development.',
    },
    {
      icon: <Rocket className="text-green-500" size={32} />,
      title: 'Efficiency',
      description: 'Our tools are built to save time and increase productivity for development teams.',
    },
  ];

  const timeline = [
    {
      children: (
        <div>
          <h4 className="font-semibold text-gray-900">Project Inception</h4>
          <p className="text-gray-600">Identified the need for automated SRS generation in the software development industry.</p>
        </div>
      ),
    },
    {
      children: (
        <div>
          <h4 className="font-semibold text-gray-900">AI Research & Development</h4>
          <p className="text-gray-600">Developed proprietary AI algorithms for understanding and generating technical documentation.</p>
        </div>
      ),
    },
    {
      children: (
        <div>
          <h4 className="font-semibold text-gray-900">Beta Launch</h4>
          <p className="text-gray-600">Released beta version to select development teams for testing and feedback.</p>
        </div>
      ),
    },
    {
      children: (
        <div>
          <h4 className="font-semibold text-gray-900">Public Release</h4>
          <p className="text-gray-600">Launched the platform publicly with full IEEE 830 compliance and multi-format export.</p>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-electric-500 py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-white space-y-6"
          >
            <h1 className="text-4xl lg:text-6xl font-bold">
              About SRS Generator
            </h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
              Revolutionizing software documentation with AI-powered technology that 
              transforms ideas into professional requirements specifications.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <Target className="text-primary-600" size={32} />
                    <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">Our Mission</h2>
                  </div>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    To democratize professional software documentation by making it accessible, 
                    efficient, and intelligent. We believe that every development team, regardless 
                    of size or resources, should have access to high-quality documentation tools 
                    that accelerate their workflow and improve project outcomes.
                  </p>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    Our AI-powered platform eliminates the traditional barriers to creating 
                    comprehensive Software Requirements Specifications, enabling teams to focus 
                    on what they do best - building amazing software.
                  </p>
                </div>
              </motion.div>
            </Col>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                  <div className="text-center space-y-6">
                    <div className="w-20 h-20 bg-gradient-to-r from-primary-600 to-electric-500 rounded-full flex items-center justify-center mx-auto">
                      <FileText className="text-white" size={40} />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">
                      Trusted by 2,500+ Teams
                    </h3>
                    <p className="text-gray-600">
                      From startups to enterprise organizations, development teams worldwide 
                      rely on our platform to streamline their documentation process.
                    </p>
                  </div>
                </div>
              </motion.div>
            </Col>
          </Row>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              These principles guide everything we do and shape the way we build 
              products that make a real difference in developers' lives.
            </p>
          </motion.div>

          <Row gutter={[32, 32]}>
            {values.map((value, index) => (
              <Col xs={24} md={12} lg={6} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card 
                    className="card h-full text-center hover:shadow-2xl transition-all duration-300"
                    bodyStyle={{ padding: '2rem' }}
                  >
                    <div className="mb-4 flex justify-center">
                      <div className="w-16 h-16 bg-gray-50 rounded-full flex items-center justify-center">
                        {value.icon}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {value.description}
                    </p>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* Journey Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600">
              From concept to reality - the story of how we're transforming 
              software documentation.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="card">
              <Timeline
                mode="left"
                items={timeline}
                className="pt-4"
              />
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Technology Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <Row gutter={[48, 48]} align="middle">
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="space-y-6">
                  <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
                    Powered by Advanced AI
                  </h2>
                  <p className="text-lg text-gray-600 leading-relaxed">
                    Our platform leverages state-of-the-art artificial intelligence and 
                    natural language processing to understand project requirements and 
                    generate comprehensive documentation that meets industry standards.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                      <span className="text-gray-700">Advanced Natural Language Processing</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                      <span className="text-gray-700">IEEE 830 Compliance Engine</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                      <span className="text-gray-700">Multi-format Export System</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                      <span className="text-gray-700">Intelligent Template Generation</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Col>
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="bg-gradient-to-r from-primary-50 to-electric-50 rounded-2xl p-8">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary-600 mb-2">99.9%</div>
                      <div className="text-sm text-gray-600">Accuracy Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary-600 mb-2">10x</div>
                      <div className="text-sm text-gray-600">Faster Generation</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary-600 mb-2">24/7</div>
                      <div className="text-sm text-gray-600">Availability</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary-600 mb-2">100%</div>
                      <div className="text-sm text-gray-600">IEEE 830 Compliant</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Col>
          </Row>
        </div>
      </section>
    </div>
  );
};

export default About;
