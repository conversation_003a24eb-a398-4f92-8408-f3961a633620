import { TECHNOLOGY_OPTIONS } from '../constants/srsFormData';

/**
 * Get technology options based on selected platforms
 * @param {Array} selectedPlatforms - Array of selected platform strings
 * @returns {Object} Technology options with showMobile flag
 */
export const getTechnologyOptions = (selectedPlatforms = []) => {
  const options = { ...TECHNOLOGY_OPTIONS };
  
  // Check if mobile platforms are selected
  const hasMobilePlatforms = selectedPlatforms.some(platform => 
    platform.includes('Mobile App') || platform.includes('mobile')
  );
  
  return {
    ...options,
    showMobile: hasMobilePlatforms
  };
};

/**
 * Calculate form completion percentage
 * @param {number} currentStep - Current step index
 * @param {number} totalSteps - Total number of steps
 * @returns {number} Completion percentage
 */
export const calculateCompletionPercentage = (currentStep, totalSteps) => {
  return Math.round(((currentStep + 1) / totalSteps) * 100);
};

/**
 * Calculate estimated time remaining
 * @param {Array} steps - Array of step objects with estimatedTime
 * @param {number} currentStep - Current step index
 * @returns {number} Estimated time in minutes
 */
export const calculateEstimatedTime = (steps, currentStep) => {
  return steps
    .slice(currentStep)
    .reduce((total, step) => total + (step.estimatedTime || 1), 0);
};

/**
 * Validate form step data
 * @param {number} stepIndex - Step index to validate
 * @param {Object} formData - Form data object
 * @param {Array} userRoles - User roles array
 * @param {Array} functionalModules - Functional modules array
 * @returns {Object} Validation result with isValid and errors
 */
export const validateStepData = (stepIndex, formData, userRoles = [], functionalModules = []) => {
  const errors = [];
  
  switch (stepIndex) {
    case 0: // Basic Project Information
      if (!formData.projectName?.trim()) {
        errors.push('Project name is required');
      }
      if (!formData.projectDescription?.trim() || formData.projectDescription.length < 50) {
        errors.push('Project description must be at least 50 characters');
      }
      if (!formData.mainGoal?.trim() || formData.mainGoal.length < 30) {
        errors.push('Main goal must be at least 30 characters');
      }
      break;
      
    case 1: // Platform & Deployment
      if (!formData.platforms?.length) {
        errors.push('At least one platform must be selected');
      }
      if (!formData.hostingPreference) {
        errors.push('Hosting preference is required');
      }
      break;
      
    case 3: // User Roles
      if (!userRoles.length || !userRoles[0].name?.trim()) {
        errors.push('At least one user role is required');
      }
      userRoles.forEach((role, index) => {
        if (!role.name?.trim()) {
          errors.push(`Role ${index + 1} name is required`);
        }
        if (!role.actions?.trim()) {
          errors.push(`Role ${index + 1} actions are required`);
        }
      });
      break;
      
    case 4: // Functional Modules
      if (!functionalModules.length || !functionalModules[0].description?.trim()) {
        errors.push('At least one functional module is required');
      }
      functionalModules.forEach((module, index) => {
        if (!module.description?.trim()) {
          errors.push(`Module ${index + 1} description is required`);
        }
      });
      break;
      
    case 6: // Development Lifecycle
      if (!formData.developmentApproach?.length) {
        errors.push('Development approach is required');
      }
      break;
      
    default:
      break;
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Generate SRS data structure
 * @param {Object} formData - Form values
 * @param {Array} userRoles - User roles array
 * @param {Array} functionalModules - Functional modules array
 * @param {Object} teamMembers - Team members object
 * @param {Array} selectedPlatforms - Selected platforms array
 * @returns {Object} Complete SRS data structure
 */
export const generateSRSData = (formData, userRoles, functionalModules, teamMembers, selectedPlatforms) => {
  return {
    id: `srs_${Date.now()}`,
    projectInfo: {
      name: formData.projectName,
      description: formData.projectDescription,
      mainGoal: formData.mainGoal
    },
    platforms: {
      selected: selectedPlatforms,
      custom: formData.customPlatform,
      hosting: formData.hostingPreference
    },
    technology: {
      frontend: formData.frontendTech || [],
      backend: formData.backendTech || [],
      mobile: formData.mobileTech || [],
      database: formData.databaseTech || [],
      storage: formData.storageTech || [],
      custom: {
        frontend: formData.customFrontend,
        backend: formData.customBackend
      }
    },
    userRoles: userRoles.filter(role => role.name?.trim()),
    functionalModules: functionalModules.filter(module => module.description?.trim()),
    integrations: {
      payment: formData.integrations_payment || [],
      authentication: formData.integrations_authentication || [],
      maps: formData.integrations_maps || [],
      analytics: formData.integrations_analytics || [],
      email: formData.integrations_email || [],
      storage: formData.integrations_storage || [],
      social: formData.integrations_social || [],
      communication: formData.integrations_communication || [],
      search: formData.integrations_search || [],
      cms: formData.integrations_cms || [],
      custom: formData.customIntegrations
    },
    development: {
      approach: formData.developmentApproach || [],
      tools: {
        projectManagement: formData.tools_projectManagement || [],
        versionControl: formData.tools_versionControl || [],
        communication: formData.tools_communication || [],
        documentation: formData.tools_documentation || [],
        design: formData.tools_design || []
      },
      customTools: formData.customTools
    },
    team: {
      composition: teamMembers,
      totalMembers: Object.values(teamMembers).reduce((sum, count) => sum + (count || 0), 0)
    },
    metadata: {
      createdAt: new Date().toISOString(),
      version: '1.0.0',
      status: 'generated'
    }
  };
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Generate random file size for demo purposes
 * @returns {string} Random file size string
 */
export const generateRandomFileSize = () => {
  const size = Math.random() * 5 + 0.5; // 0.5 to 5.5 MB
  return `${size.toFixed(1)} MB`;
};

/**
 * Debounce function for performance optimization
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
