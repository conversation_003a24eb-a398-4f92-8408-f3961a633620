import React from 'react';
import { <PERSON>, Divider, Anchor } from 'antd';
import { FileText, Shield, Users, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

const Terms = () => {
  const sections = [
    { key: 'acceptance', title: '1. Acceptance of Terms' },
    { key: 'description', title: '2. Description of Service' },
    { key: 'user-accounts', title: '3. User Accounts and Registration' },
    { key: 'acceptable-use', title: '4. Acceptable Use Policy' },
    { key: 'intellectual-property', title: '5. Intellectual Property Rights' },
    { key: 'privacy', title: '6. Privacy and Data Protection' },
    { key: 'payment', title: '7. Payment Terms' },
    { key: 'termination', title: '8. Termination' },
    { key: 'disclaimers', title: '9. Disclaimers and Limitations' },
    { key: 'governing-law', title: '10. Governing Law' },
    { key: 'contact', title: '11. Contact Information' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-electric-500 py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-white space-y-6"
          >
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <FileText className="text-white" size={32} />
              </div>
            </div>
            <h1 className="text-4xl lg:text-6xl font-bold">
              Terms & Conditions
            </h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
              Please read these terms carefully before using our SRS Generator platform.
            </p>
            <p className="text-lg text-blue-200">
              Last updated: December 2024
            </p>
          </motion.div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Table of Contents */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Card className="card sticky top-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Table of Contents
                  </h3>
                  <Anchor
                    direction="vertical"
                    items={sections}
                    className="[&_.ant-anchor-link-title]:text-gray-600 [&_.ant-anchor-link-title]:text-sm"
                  />
                </Card>
              </motion.div>
            </div>

            {/* Terms Content */}
            <div className="lg:col-span-3">
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Card className="card">
                  <div className="prose prose-lg max-w-none">
                    {/* Introduction */}
                    <div className="mb-8">
                      <div className="flex items-center space-x-3 mb-4">
                        <AlertCircle className="text-amber-500" size={24} />
                        <h2 className="text-2xl font-bold text-gray-900 m-0">
                          Important Notice
                        </h2>
                      </div>
                      <p className="text-gray-600 leading-relaxed">
                        These Terms and Conditions ("Terms") govern your use of the SRS Generator 
                        platform operated by SRS Generator Inc. ("we," "us," or "our"). By accessing 
                        or using our service, you agree to be bound by these Terms.
                      </p>
                    </div>

                    <Divider />

                    {/* Section 1 */}
                    <div id="acceptance" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        1. Acceptance of Terms
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        By creating an account or using our SRS Generator platform, you acknowledge 
                        that you have read, understood, and agree to be bound by these Terms and 
                        our Privacy Policy.
                      </p>
                      <p className="text-gray-600 leading-relaxed">
                        If you do not agree to these Terms, you may not access or use our service.
                      </p>
                    </div>

                    {/* Section 2 */}
                    <div id="description" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        2. Description of Service
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        SRS Generator is an AI-powered platform that helps users create Software 
                        Requirements Specification documents. Our service includes:
                      </p>
                      <ul className="list-disc list-inside text-gray-600 space-y-2 mb-4">
                        <li>AI-powered document generation</li>
                        <li>IEEE 830 compliant templates</li>
                        <li>Multi-format export capabilities</li>
                        <li>Project history and version control</li>
                        <li>Team collaboration features</li>
                      </ul>
                    </div>

                    {/* Section 3 */}
                    <div id="user-accounts" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        3. User Accounts and Registration
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        To access certain features of our service, you must create an account. 
                        You agree to:
                      </p>
                      <ul className="list-disc list-inside text-gray-600 space-y-2 mb-4">
                        <li>Provide accurate and complete information</li>
                        <li>Maintain the security of your account credentials</li>
                        <li>Notify us immediately of any unauthorized access</li>
                        <li>Accept responsibility for all activities under your account</li>
                      </ul>
                    </div>

                    {/* Section 4 */}
                    <div id="acceptable-use" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        4. Acceptable Use Policy
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        You agree not to use our service to:
                      </p>
                      <ul className="list-disc list-inside text-gray-600 space-y-2 mb-4">
                        <li>Violate any applicable laws or regulations</li>
                        <li>Infringe on intellectual property rights</li>
                        <li>Generate harmful, offensive, or inappropriate content</li>
                        <li>Attempt to reverse engineer or compromise our systems</li>
                        <li>Share your account with unauthorized users</li>
                      </ul>
                    </div>

                    {/* Section 5 */}
                    <div id="intellectual-property" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        5. Intellectual Property Rights
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        The SRS Generator platform, including its software, design, and content, 
                        is owned by us and protected by intellectual property laws. You retain 
                        ownership of the content you create using our platform.
                      </p>
                      <p className="text-gray-600 leading-relaxed">
                        You grant us a limited license to process your content solely for the 
                        purpose of providing our service.
                      </p>
                    </div>

                    {/* Section 6 */}
                    <div id="privacy" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        6. Privacy and Data Protection
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        We are committed to protecting your privacy and handling your data 
                        responsibly. Our Privacy Policy explains how we collect, use, and 
                        protect your information.
                      </p>
                      <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
                        <Shield className="text-blue-600" size={20} />
                        <span className="text-blue-800 text-sm">
                          Your data is encrypted and stored securely using industry-standard practices.
                        </span>
                      </div>
                    </div>

                    {/* Section 7 */}
                    <div id="payment" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        7. Payment Terms
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        Certain features of our service may require payment. By subscribing to 
                        a paid plan, you agree to:
                      </p>
                      <ul className="list-disc list-inside text-gray-600 space-y-2 mb-4">
                        <li>Pay all fees as described in your chosen plan</li>
                        <li>Provide accurate billing information</li>
                        <li>Authorize automatic renewal unless cancelled</li>
                        <li>Accept our refund policy as stated</li>
                      </ul>
                    </div>

                    {/* Section 8 */}
                    <div id="termination" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        8. Termination
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        Either party may terminate this agreement at any time. Upon termination:
                      </p>
                      <ul className="list-disc list-inside text-gray-600 space-y-2 mb-4">
                        <li>Your access to the service will be suspended</li>
                        <li>You may download your data within 30 days</li>
                        <li>We may delete your data after the grace period</li>
                        <li>Outstanding fees remain due and payable</li>
                      </ul>
                    </div>

                    {/* Section 9 */}
                    <div id="disclaimers" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        9. Disclaimers and Limitations
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        Our service is provided "as is" without warranties of any kind. We do not 
                        guarantee that the service will be uninterrupted, error-free, or meet 
                        your specific requirements.
                      </p>
                      <p className="text-gray-600 leading-relaxed">
                        Our liability is limited to the maximum extent permitted by law.
                      </p>
                    </div>

                    {/* Section 10 */}
                    <div id="governing-law" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        10. Governing Law
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        These Terms are governed by the laws of the State of California, United States. 
                        Any disputes will be resolved in the courts of San Francisco County, California.
                      </p>
                    </div>

                    {/* Section 11 */}
                    <div id="contact" className="mb-8">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        11. Contact Information
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        If you have questions about these Terms, please contact us:
                      </p>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="space-y-2 text-gray-600">
                          <p><strong>Email:</strong> <EMAIL></p>
                          <p><strong>Address:</strong> 123 Tech Street, San Francisco, CA 94105</p>
                          <p><strong>Phone:</strong> +****************</p>
                        </div>
                      </div>
                    </div>

                    <Divider />

                    <div className="text-center text-gray-500 text-sm">
                      <p>
                        These Terms and Conditions are effective as of December 2024 and may be 
                        updated from time to time. Continued use of our service constitutes 
                        acceptance of any changes.
                      </p>
                    </div>
                  </div>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Terms;
