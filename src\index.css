@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

/* Custom base styles */
* {
  border-color: theme("colors.gray.200");
}

body {
  font-family: "Inter", ui-sans-serif, system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans",
    sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

/* Custom component styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.card {
  @apply bg-white rounded-xl shadow-lg border border-gray-100 p-6 transition-all duration-200 hover:shadow-xl;
}

.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent;
}

.hero-gradient {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

.form-textarea {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none;
}

/* Custom animations */
@keyframes gradient {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Utility classes */
.text-balance {
  text-wrap: balance;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

/* Enhanced SRS Document Styling */
.srs-content {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  line-height: 1.7;
  color: #374151;
}

.srs-content h1 {
  color: #1f2937;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.srs-content h2 {
  color: #1f2937;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.srs-content h3,
.srs-content h4,
.srs-content h5,
.srs-content h6 {
  color: #374151;
  font-weight: 600;
}

/* Enhanced Table Styling for SRS */
.srs-content table,
.srs-table {
  width: 100%;
  border-collapse: collapse;
  margin: 24px 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.srs-content table thead,
.srs-table thead {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.srs-content table th,
.srs-table th {
  padding: 16px 20px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  border: none;
}

.srs-content table td,
.srs-table td {
  padding: 14px 20px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
  vertical-align: top;
}

.srs-content table tbody tr:hover,
.srs-table tbody tr:hover {
  background-color: #f9fafb;
  transition: background-color 0.2s ease;
}

.srs-content table tbody tr:last-child td,
.srs-table tbody tr:last-child td {
  border-bottom: none;
}

/* Timeline specific table styling */
.timeline-table {
  margin: 32px 0;
  border: 2px solid #e5e7eb;
}

.timeline-table thead {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.timeline-table th {
  color: white;
  font-weight: 700;
  padding: 18px 24px;
  font-size: 15px;
}

.timeline-table td {
  padding: 16px 24px;
  font-size: 14px;
  line-height: 1.6;
}

.timeline-table tbody tr:nth-child(even) {
  background-color: #f8fafc;
}

.timeline-table tbody tr:nth-child(odd) {
  background-color: white;
}

/* Code block enhancements */
.srs-content pre {
  background: #1f2937;
  color: #10b981;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 20px 0;
  border-left: 4px solid #10b981;
  font-family: "JetBrains Mono", "Fira Code", "Consolas", monospace;
  font-size: 13px;
  line-height: 1.5;
}

.srs-content code {
  background: #f3f4f6;
  color: #1f2937;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: "JetBrains Mono", "Fira Code", "Consolas", monospace;
  font-size: 13px;
  border: 1px solid #d1d5db;
}

/* List styling */
.srs-content ul,
.srs-content ol {
  margin: 16px 0;
  padding-left: 0;
}

.srs-content li {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
  line-height: 1.6;
}

/* Responsive design for tables */
@media (max-width: 768px) {
  .srs-content table,
  .srs-table,
  .timeline-table {
    font-size: 12px;
  }

  .srs-content table th,
  .srs-content table td,
  .srs-table th,
  .srs-table td,
  .timeline-table th,
  .timeline-table td {
    padding: 10px 12px;
  }
}
