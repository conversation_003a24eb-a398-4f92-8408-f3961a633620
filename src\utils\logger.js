/**
 * Optimized Logger Utility
 * Prevents excessive logging and provides structured logging for production
 */

class Logger {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.logLevel = this.isDevelopment ? 'debug' : 'error';
    this.logHistory = [];
    this.maxHistorySize = 100;
    this.rateLimitMap = new Map();
    this.rateLimitWindow = 5000; // 5 seconds
    this.maxLogsPerWindow = 10;
  }

  // Rate limiting to prevent log spam
  isRateLimited(key) {
    const now = Date.now();
    const windowStart = now - this.rateLimitWindow;
    
    if (!this.rateLimitMap.has(key)) {
      this.rateLimitMap.set(key, []);
    }
    
    const logs = this.rateLimitMap.get(key);
    
    // Remove old logs outside the window
    const recentLogs = logs.filter(timestamp => timestamp > windowStart);
    this.rateLimitMap.set(key, recentLogs);
    
    // Check if we've exceeded the limit
    if (recentLogs.length >= this.maxLogsPerWindow) {
      return true;
    }
    
    // Add current log timestamp
    recentLogs.push(now);
    this.rateLimitMap.set(key, recentLogs);
    
    return false;
  }

  // Create structured log entry
  createLogEntry(level, message, data = null, context = null) {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      context,
      url: window.location.href,
      userAgent: navigator.userAgent.substring(0, 100), // Truncate for storage
      sessionId: this.getSessionId()
    };
  }

  // Get or create session ID
  getSessionId() {
    let sessionId = sessionStorage.getItem('logger_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      sessionStorage.setItem('logger_session_id', sessionId);
    }
    return sessionId;
  }

  // Add to log history
  addToHistory(logEntry) {
    this.logHistory.push(logEntry);
    
    // Keep only recent logs
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory = this.logHistory.slice(-this.maxHistorySize);
    }
    
    // Store in localStorage for debugging (development only)
    if (this.isDevelopment) {
      try {
        localStorage.setItem('app_log_history', JSON.stringify(this.logHistory.slice(-20)));
      } catch (err) {
        // Ignore localStorage errors
      }
    }
  }

  // Debug level logging
  debug(message, data = null, context = null) {
    if (!this.isDevelopment) return;
    
    const key = `debug_${message}`;
    if (this.isRateLimited(key)) {
      return;
    }
    
    const logEntry = this.createLogEntry('debug', message, data, context);
    this.addToHistory(logEntry);
    
    console.debug('🐛 [DEBUG]', message, data ? { data, context } : '');
  }

  // Info level logging
  info(message, data = null, context = null) {
    const key = `info_${message}`;
    if (this.isRateLimited(key)) {
      return;
    }
    
    const logEntry = this.createLogEntry('info', message, data, context);
    this.addToHistory(logEntry);
    
    if (this.isDevelopment) {
      console.info('ℹ️ [INFO]', message, data ? { data, context } : '');
    }
  }

  // Warning level logging
  warn(message, data = null, context = null) {
    const key = `warn_${message}`;
    if (this.isRateLimited(key)) {
      return;
    }
    
    const logEntry = this.createLogEntry('warn', message, data, context);
    this.addToHistory(logEntry);
    
    console.warn('⚠️ [WARN]', message, data ? { data, context } : '');
  }

  // Error level logging
  error(message, error = null, context = null) {
    const key = `error_${message}`;
    if (this.isRateLimited(key)) {
      return;
    }
    
    const errorData = error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...error
    } : null;
    
    const logEntry = this.createLogEntry('error', message, errorData, context);
    this.addToHistory(logEntry);
    
    console.error('🚨 [ERROR]', message, error || '', context ? { context } : '');
    
    // In production, send to error reporting service
    if (!this.isDevelopment) {
      this.reportError(logEntry);
    }
  }

  // Performance logging
  performance(operation, duration, data = null) {
    if (!this.isDevelopment) return;
    
    const key = `perf_${operation}`;
    if (this.isRateLimited(key)) {
      return;
    }
    
    const logEntry = this.createLogEntry('performance', `${operation} took ${duration}ms`, {
      operation,
      duration,
      ...data
    });
    
    this.addToHistory(logEntry);
    
    const emoji = duration > 1000 ? '🐌' : duration > 500 ? '⏱️' : '⚡';
    console.log(`${emoji} [PERF] ${operation}: ${duration}ms`, data || '');
  }

  // API call logging
  apiCall(method, url, status, duration, data = null) {
    const key = `api_${method}_${url}`;
    if (this.isRateLimited(key)) {
      return;
    }
    
    const logEntry = this.createLogEntry('api', `${method} ${url} - ${status}`, {
      method,
      url,
      status,
      duration,
      ...data
    });
    
    this.addToHistory(logEntry);
    
    if (this.isDevelopment) {
      const emoji = status >= 400 ? '❌' : status >= 300 ? '🔄' : '✅';
      console.log(`${emoji} [API] ${method} ${url} - ${status} (${duration}ms)`, data || '');
    }
  }

  // User action logging
  userAction(action, data = null) {
    if (!this.isDevelopment) return;
    
    const key = `user_${action}`;
    if (this.isRateLimited(key)) {
      return;
    }
    
    const logEntry = this.createLogEntry('user', action, data);
    this.addToHistory(logEntry);
    
    console.log('👤 [USER]', action, data || '');
  }

  // Report error to external service (production)
  reportError(logEntry) {
    try {
      // Store in localStorage for now (in production, send to API)
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(logEntry);
      
      // Keep only last 20 errors
      const recentErrors = existingErrors.slice(-20);
      localStorage.setItem('app_errors', JSON.stringify(recentErrors));
      
      // Future: Send to error reporting service
      // fetch('/api/errors', { method: 'POST', body: JSON.stringify(logEntry) });
    } catch (err) {
      // Ignore errors in error reporting
    }
  }

  // Get log history for debugging
  getHistory() {
    return this.logHistory;
  }

  // Clear log history
  clearHistory() {
    this.logHistory = [];
    if (this.isDevelopment) {
      localStorage.removeItem('app_log_history');
    }
  }

  // Get error statistics
  getErrorStats() {
    const errors = this.logHistory.filter(log => log.level === 'error');
    const warnings = this.logHistory.filter(log => log.level === 'warn');
    
    return {
      totalLogs: this.logHistory.length,
      errors: errors.length,
      warnings: warnings.length,
      recentErrors: errors.slice(-5),
      recentWarnings: warnings.slice(-5)
    };
  }
}

// Create singleton instance
const logger = new Logger();

// Performance measurement utility
export const measurePerformance = (operation) => {
  const start = performance.now();
  
  return {
    end: (data = null) => {
      const duration = Math.round(performance.now() - start);
      logger.performance(operation, duration, data);
      return duration;
    }
  };
};

// API call wrapper with automatic logging
export const loggedFetch = async (url, options = {}) => {
  const method = options.method || 'GET';
  const start = performance.now();
  
  try {
    const response = await fetch(url, options);
    const duration = Math.round(performance.now() - start);
    
    logger.apiCall(method, url, response.status, duration, {
      ok: response.ok,
      statusText: response.statusText
    });
    
    return response;
  } catch (error) {
    const duration = Math.round(performance.now() - start);
    
    logger.apiCall(method, url, 0, duration, {
      error: error.message
    });
    
    throw error;
  }
};

export default logger;
