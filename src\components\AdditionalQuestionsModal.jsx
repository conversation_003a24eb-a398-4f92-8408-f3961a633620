import React, { useState } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  Typography,
  Space,
  Alert,
  Select,
} from "antd";

const { Title, Text } = Typography;
const { TextArea } = Input;

const AdditionalQuestionsModal = ({
  visible,
  onCancel,
  onSubmit,
  questions = [],
  message = "",
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [answers, setAnswers] = useState({});
  console.log(questions, "Questions in Modal");
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error("Form validation failed:", error);
    }
  };

  // Handle skip - let GPT auto-analyze user information for 100% accurate answers
  const handleSkip = () => {
    onSubmit({
      skipQuestions: true,
      autoAnalyze: true,
      message:
        "User chose to skip questions. Please analyze the provided user information and auto-generate 100% accurate answers based on the project data.",
    });
  };

  const handleInputChange = (questionId, value) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const renderQuestionInput = (question, index) => {
    // Safely destructure with fallbacks to prevent undefined errors
    const {
      id = `question_${index}_${Date.now()}`, // Unique ID to prevent duplicate keys
      question: questionText = question || "Please provide information",
      type = "text",
      required = true,
      options = [],
    } = question || {};

    // Ensure questionText is a string
    const safeQuestionText = String(
      questionText || "Please provide information"
    );

    // Create unique key for React rendering
    const uniqueKey = `${id}_${index}`;

    switch (type?.toLowerCase()) {
      case "textarea":
        return (
          <Form.Item
            key={uniqueKey}
            name={id}
            label={safeQuestionText}
            rules={[
              {
                required,
                message: `Please provide ${safeQuestionText?.toLowerCase()}`,
              },
            ]}
          >
            <TextArea
              rows={4}
              placeholder="Please provide detailed information..."
              onChange={(e) => handleInputChange(id, e.target.value)}
            />
          </Form.Item>
        );

      case "select":
        return (
          <Form.Item
            key={uniqueKey}
            name={id}
            label={safeQuestionText}
            rules={[
              {
                required,
                message: `Please select ${safeQuestionText?.toLowerCase()}`,
              },
            ]}
          >
            <Select
              placeholder="Please select an option"
              onChange={(value) => handleInputChange(id, value)}
            >
              {options?.map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        );

      case "multiselect":
        return (
          <Form.Item
            key={uniqueKey}
            name={id}
            label={safeQuestionText}
            rules={[
              {
                required,
                message: `Please select ${safeQuestionText?.toLowerCase()}`,
              },
            ]}
          >
            <Select
              mode="multiple"
              placeholder="Please select options"
              onChange={(value) => handleInputChange(id, value)}
            >
              {options?.map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        );

      case "text":
      default:
        return (
          <Form.Item
            key={uniqueKey}
            name={id}
            label={safeQuestionText}
            rules={[
              {
                required,
                message: `Please provide ${safeQuestionText?.toLowerCase()}`,
              },
            ]}
          >
            <Input
              placeholder="Please provide the information..."
              onChange={(e) => handleInputChange(id, e.target.value)}
            />
          </Form.Item>
        );
    }
  };

  return (
    <Modal
      title={
        <div style={{ textAlign: "center" }}>
          <Title level={3} style={{ margin: 0, color: "#1890ff" }}>
            📋 Additional Information Required
          </Title>
          <Text type="secondary">ChatGPT-style Interactive SRS Generation</Text>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel} disabled={loading}>
          Cancel
        </Button>,
        <Button
          key="skip"
          onClick={handleSkip}
          disabled={loading}
          style={{
            background: "#52c41a",
            borderColor: "#52c41a",
            color: "white",
          }}
        >
          🤖 Skip & Auto-Analyze
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          loading={loading}
          style={{ background: "#1890ff" }}
        >
          Submit Answers
        </Button>,
      ]}
    >
      <div style={{ padding: "20px 0" }}>
        {message && (
          <Alert
            message="Information Required"
            description={message}
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <div style={{ marginBottom: 24 }}>
          <Text strong style={{ fontSize: "16px" }}>
            🤖 ChatGPT needs additional information to generate a comprehensive
            SRS document:
          </Text>
          <div
            style={{
              marginTop: 12,
              padding: 12,
              background: "#e6f7ff",
              borderRadius: 6,
              border: "1px solid #91d5ff",
            }}
          >
            <Text style={{ fontSize: "14px", color: "#1890ff" }}>
              💡 <strong>Pro Tip:</strong> You can either answer these questions
              manually or click
              <strong> "Skip & Auto-Analyze"</strong> to let GPT automatically
              analyze your project data and generate 100% accurate answers based
              on your information.
            </Text>
          </div>
        </div>

        <Form form={form} layout="vertical" requiredMark="optional">
          <Space direction="vertical" size="large" style={{ width: "100%" }}>
            {Array.isArray(questions) && questions.length > 0 ? (
              questions.map((question, index) =>
                question ? (
                  renderQuestionInput(question, index)
                ) : (
                  <div key={`fallback-${index}`}>
                    <Text type="secondary">Question data unavailable</Text>
                  </div>
                )
              )
            ) : (
              <div>
                <Text type="secondary">No additional questions available</Text>
              </div>
            )}
          </Space>
        </Form>

        <div
          style={{
            marginTop: 24,
            padding: 16,
            background: "#f6f8fa",
            borderRadius: 8,
          }}
        >
          <Text type="secondary" style={{ fontSize: "14px" }}>
            💡 <strong>Tip:</strong> The more detailed information you provide,
            the more accurate and comprehensive your SRS document will be. This
            interactive process ensures we capture all requirements just like
            ChatGPT does manually.
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default AdditionalQuestionsModal;
