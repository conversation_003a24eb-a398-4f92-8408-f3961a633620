import { useState, useEffect, useCallback } from 'react';

/**
 * IndexedDB utility functions
 */
const openDB = (dbName, version = 1, upgradeCallback) => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(dbName, version);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (upgradeCallback) {
        upgradeCallback(db, event.oldVersion, event.newVersion);
      }
    };
  });
};

const performTransaction = (db, storeName, mode, operation) => {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([storeName], mode);
    const store = transaction.objectStore(storeName);
    
    transaction.onerror = () => reject(transaction.error);
    transaction.oncomplete = () => resolve();
    
    const request = operation(store);
    if (request) {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    }
  });
};

/**
 * Custom hook for IndexedDB operations
 * @param {string} dbName - Database name
 * @param {string} storeName - Object store name
 * @param {number} version - Database version
 * @returns {Object} IndexedDB operations
 */
export const useIndexedDB = (dbName, storeName, version = 1) => {
  const [db, setDb] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize database
  useEffect(() => {
    const initDB = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const database = await openDB(dbName, version, (db, oldVersion, newVersion) => {
          // Create object store if it doesn't exist
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, { 
              keyPath: 'id', 
              autoIncrement: true 
            });
            
            // Create indexes
            store.createIndex('timestamp', 'timestamp', { unique: false });
            store.createIndex('type', 'type', { unique: false });
          }
        });
        
        setDb(database);
      } catch (err) {
        console.error('Error initializing IndexedDB:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    initDB();
  }, [dbName, storeName, version]);

  // Add data to store
  const addData = useCallback(async (data) => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      const dataWithTimestamp = {
        ...data,
        timestamp: new Date().toISOString(),
        id: data.id || `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      
      const result = await performTransaction(db, storeName, 'readwrite', (store) => 
        store.add(dataWithTimestamp)
      );
      
      return result;
    } catch (err) {
      console.error('Error adding data to IndexedDB:', err);
      throw err;
    }
  }, [db, storeName]);

  // Get data by ID
  const getData = useCallback(async (id) => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      const result = await performTransaction(db, storeName, 'readonly', (store) => 
        store.get(id)
      );
      
      return result;
    } catch (err) {
      console.error('Error getting data from IndexedDB:', err);
      throw err;
    }
  }, [db, storeName]);

  // Get all data
  const getAllData = useCallback(async () => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      const result = await performTransaction(db, storeName, 'readonly', (store) => 
        store.getAll()
      );
      
      return result || [];
    } catch (err) {
      console.error('Error getting all data from IndexedDB:', err);
      throw err;
    }
  }, [db, storeName]);

  // Update data
  const updateData = useCallback(async (id, updates) => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      const existing = await getData(id);
      if (!existing) throw new Error('Data not found');
      
      const updatedData = {
        ...existing,
        ...updates,
        updatedAt: new Date().toISOString()
      };
      
      const result = await performTransaction(db, storeName, 'readwrite', (store) => 
        store.put(updatedData)
      );
      
      return result;
    } catch (err) {
      console.error('Error updating data in IndexedDB:', err);
      throw err;
    }
  }, [db, storeName, getData]);

  // Delete data
  const deleteData = useCallback(async (id) => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      await performTransaction(db, storeName, 'readwrite', (store) => 
        store.delete(id)
      );
      
      return true;
    } catch (err) {
      console.error('Error deleting data from IndexedDB:', err);
      throw err;
    }
  }, [db, storeName]);

  // Clear all data
  const clearAll = useCallback(async () => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      await performTransaction(db, storeName, 'readwrite', (store) => 
        store.clear()
      );
      
      return true;
    } catch (err) {
      console.error('Error clearing IndexedDB:', err);
      throw err;
    }
  }, [db, storeName]);

  // Search data by index
  const searchByIndex = useCallback(async (indexName, value) => {
    if (!db) throw new Error('Database not initialized');
    
    try {
      const result = await performTransaction(db, storeName, 'readonly', (store) => {
        const index = store.index(indexName);
        return index.getAll(value);
      });
      
      return result || [];
    } catch (err) {
      console.error('Error searching IndexedDB:', err);
      throw err;
    }
  }, [db, storeName]);

  // Get storage usage
  const getStorageUsage = useCallback(async () => {
    if (!navigator.storage || !navigator.storage.estimate) {
      return { usage: 0, quota: 0 };
    }
    
    try {
      const estimate = await navigator.storage.estimate();
      return {
        usage: estimate.usage || 0,
        quota: estimate.quota || 0,
        usagePercentage: estimate.quota ? (estimate.usage / estimate.quota) * 100 : 0
      };
    } catch (err) {
      console.error('Error getting storage usage:', err);
      return { usage: 0, quota: 0, usagePercentage: 0 };
    }
  }, []);

  return {
    db,
    isLoading,
    error,
    addData,
    getData,
    getAllData,
    updateData,
    deleteData,
    clearAll,
    searchByIndex,
    getStorageUsage,
    isSupported: !!window.indexedDB
  };
};

/**
 * Hook for SRS data management with IndexedDB
 * @returns {Object} SRS data operations
 */
export const useSRSIndexedDB = () => {
  const {
    addData,
    getData,
    getAllData,
    updateData,
    deleteData,
    clearAll,
    searchByIndex,
    isLoading,
    error,
    isSupported
  } = useIndexedDB('SRSGeneratorDB', 'srs_documents', 1);

  const [documents, setDocuments] = useState([]);

  // Load all documents on mount
  useEffect(() => {
    const loadDocuments = async () => {
      try {
        if (!isLoading && isSupported) {
          const allDocs = await getAllData();
          setDocuments(allDocs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)));
        }
      } catch (err) {
        console.error('Error loading documents:', err);
      }
    };

    loadDocuments();
  }, [getAllData, isLoading, isSupported]);

  // Add SRS document
  const addSRSDocument = useCallback(async (srsData) => {
    try {
      const docData = {
        ...srsData,
        type: 'srs_document',
        size: JSON.stringify(srsData).length
      };
      
      const id = await addData(docData);
      const newDoc = { ...docData, id };
      
      setDocuments(prev => [newDoc, ...prev]);
      return id;
    } catch (err) {
      console.error('Error adding SRS document:', err);
      throw err;
    }
  }, [addData]);

  // Update SRS document
  const updateSRSDocument = useCallback(async (id, updates) => {
    try {
      await updateData(id, updates);
      setDocuments(prev => 
        prev.map(doc => doc.id === id ? { ...doc, ...updates } : doc)
      );
    } catch (err) {
      console.error('Error updating SRS document:', err);
      throw err;
    }
  }, [updateData]);

  // Delete SRS document
  const deleteSRSDocument = useCallback(async (id) => {
    try {
      await deleteData(id);
      setDocuments(prev => prev.filter(doc => doc.id !== id));
    } catch (err) {
      console.error('Error deleting SRS document:', err);
      throw err;
    }
  }, [deleteData]);

  return {
    documents,
    addSRSDocument,
    updateSRSDocument,
    deleteSRSDocument,
    clearAllDocuments: clearAll,
    isLoading,
    error,
    isSupported
  };
};
