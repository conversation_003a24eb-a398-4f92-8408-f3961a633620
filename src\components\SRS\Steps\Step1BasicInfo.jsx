import { Card, Row, Col, Form, Input, Typography } from "antd";
import { FileText } from "lucide-react";
import { motion } from "framer-motion";

const { TextArea } = Input;
const { Title } = Typography;

const Step1BasicInfo = ({ form }) => {
  // Sync form data on field changes to ensure localStorage capture
  const handleFieldChange = (field, value) => {
    form.setFieldValue(field, value);
    console.log(`📝 Step1: ${field} updated:`, value);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <FileText className="mr-3 text-blue-600" size={24} />
          Basic Project Information
        </Title>

        <Row gutter={[24, 24]}>
          <Col xs={24}>
            <Form.Item
              name="projectName"
              label="Project Name"
              rules={[{ required: true, message: "Please enter project name" }]}
            >
              <Input
                placeholder="Enter your project name"
                size="large"
                className="rounded-lg"
                value={form.getFieldValue("projectName")}
                onChange={(e) =>
                  handleFieldChange("projectName", e.target.value)
                }
              />
            </Form.Item>
          </Col>

          <Col xs={24}>
            <Form.Item
              name="projectDescription"
              label="Project Description"
              rules={[
                { required: true, message: "Please enter project description" },
                {
                  min: 50,
                  message: "Description should be at least 50 characters",
                },
              ]}
            >
              <TextArea
                rows={4}
                placeholder="Describe your project in detail (minimum 50 characters)"
                showCount
                maxLength={1000}
                className="rounded-lg"
                value={form.getFieldValue("projectDescription")}
                onChange={(e) =>
                  handleFieldChange("projectDescription", e.target.value)
                }
              />
            </Form.Item>
          </Col>

          <Col xs={24}>
            <Form.Item
              name="mainGoal"
              label="Main Goal/Problem Solved"
              rules={[
                { required: true, message: "Please describe the main goal" },
                {
                  min: 30,
                  message: "Goal description should be at least 30 characters",
                },
              ]}
            >
              <TextArea
                rows={3}
                placeholder="What is the main problem this project solves?"
                showCount
                maxLength={500}
                className="rounded-lg"
                value={form.getFieldValue("mainGoal")}
                onChange={(e) => handleFieldChange("mainGoal", e.target.value)}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </motion.div>
  );
};

export default Step1BasicInfo;
