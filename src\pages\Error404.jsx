import React from 'react';
import { Button, Typography, Space } from 'antd';
import { Home, ArrowLeft, Search, FileText } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const { Title, Text, Paragraph } = Typography;

const Error404 = () => {
  const navigate = useNavigate();

  const popularPages = [
    { path: '/', label: 'Home', icon: <Home size={16} /> },
    { path: '/generate', label: 'Generate SRS', icon: <FileText size={16} /> },
    { path: '/history', label: 'History', icon: <Search size={16} /> },
    { path: '/about', label: 'About', icon: <FileText size={16} /> },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* 404 Animation */}
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="text-9xl font-bold text-blue-200 select-none">
              404
            </div>
            <motion.div
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                repeatDelay: 2
              }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            >
              <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full flex items-center justify-center shadow-lg">
                <Search className="text-white" size={32} />
              </div>
            </motion.div>
          </motion.div>

          {/* Error Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-4"
          >
            <Title level={2} className="text-gray-800 mb-4">
              Oops! Page Not Found
            </Title>
            <Paragraph className="text-lg text-gray-600 max-w-lg mx-auto">
              The page you're looking for seems to have wandered off into the digital void. 
              Don't worry, it happens to the best of us!
            </Paragraph>
          </motion.div>

          {/* Suggestions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white rounded-xl shadow-lg p-6 space-y-6"
          >
            <div>
              <Title level={4} className="text-gray-800 mb-4">
                Here's what you can do:
              </Title>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="text-left space-y-2">
                  <Text className="text-gray-600">• Check the URL for typos</Text>
                  <Text className="text-gray-600">• Use the navigation menu</Text>
                  <Text className="text-gray-600">• Go back to the previous page</Text>
                  <Text className="text-gray-600">• Visit our homepage</Text>
                </div>
                <div className="text-left space-y-2">
                  <Text className="text-gray-600">• Search for what you need</Text>
                  <Text className="text-gray-600">• Check our popular pages</Text>
                  <Text className="text-gray-600">• Contact support if needed</Text>
                  <Text className="text-gray-600">• Try refreshing the page</Text>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <Space size="large" wrap className="justify-center">
                <Button
                  type="primary"
                  size="large"
                  icon={<Home size={16} />}
                  className="btn-primary"
                >
                  <Link to="/">Go Home</Link>
                </Button>
                
                <Button
                  size="large"
                  icon={<ArrowLeft size={16} />}
                  onClick={() => navigate(-1)}
                  className="btn-secondary"
                >
                  Go Back
                </Button>
              </Space>

              {/* Popular Pages */}
              <div className="pt-4 border-t border-gray-200">
                <Text className="text-sm text-gray-500 mb-3 block">
                  Or visit one of our popular pages:
                </Text>
                <Space wrap className="justify-center">
                  {popularPages.map((page) => (
                    <Link key={page.path} to={page.path}>
                      <Button
                        type="text"
                        icon={page.icon}
                        className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        {page.label}
                      </Button>
                    </Link>
                  ))}
                </Space>
              </div>
            </div>
          </motion.div>

          {/* Fun Fact */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="bg-blue-50 rounded-lg p-4"
          >
            <Text className="text-sm text-blue-700">
              <strong>Fun Fact:</strong> The 404 error code was named after room 404 at CERN, 
              where the World Wide Web was invented. The room contained the central database, 
              and when it was moved, people got "404 - Room Not Found" errors!
            </Text>
          </motion.div>

          {/* Contact Support */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 1 }}
            className="text-center"
          >
            <Text className="text-gray-500">
              Still having trouble? {' '}
              <Link to="/contact" className="text-blue-600 hover:text-blue-700 font-medium">
                Contact our support team
              </Link>
              {' '} and we'll help you out!
            </Text>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default Error404;
