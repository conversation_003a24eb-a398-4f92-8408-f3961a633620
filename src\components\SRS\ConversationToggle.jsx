import React, { useState } from 'react';
import { Switch, Tooltip, Card, Typography, Space, Tag } from 'antd';
import { MessageOutlined, DollarOutlined, ThunderboltOutlined, HistoryOutlined } from '@ant-design/icons';

const { Text, Title } = Typography;

const ConversationToggle = ({ 
  projectId, 
  conversationSummary, 
  onToggleChange, 
  defaultValue = true 
}) => {
  const [useOldConversation, setUseOldConversation] = useState(defaultValue);

  const handleToggleChange = (checked) => {
    setUseOldConversation(checked);
    onToggleChange(checked);
  };

  const formatCost = (cost) => {
    return cost ? `$${cost.toFixed(3)}` : '$0.000';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card 
      size="small" 
      className="conversation-toggle-card"
      style={{ 
        marginBottom: 16,
        border: useOldConversation ? '2px solid #1890ff' : '2px solid #52c41a',
        borderRadius: 8
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {/* Toggle Switch */}
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Switch
            checked={useOldConversation}
            onChange={handleToggleChange}
            checkedChildren={<MessageOutlined />}
            unCheckedChildren={<ThunderboltOutlined />}
            style={{ 
              backgroundColor: useOldConversation ? '#1890ff' : '#52c41a'
            }}
          />
          
          <div>
            <Title level={5} style={{ margin: 0, color: useOldConversation ? '#1890ff' : '#52c41a' }}>
              {useOldConversation ? '💬 Continue Conversation' : '🆕 Fresh Start'}
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {useOldConversation 
                ? 'Use conversation memory for context-aware editing'
                : 'Start new conversation for cost-effective editing'
              }
            </Text>
          </div>
        </div>

        {/* Conversation Info */}
        {conversationSummary && (
          <div style={{ textAlign: 'right' }}>
            <Space direction="vertical" size={2}>
              <div style={{ display: 'flex', gap: 8, justifyContent: 'flex-end' }}>
                <Tag icon={<MessageOutlined />} color="blue">
                  {conversationSummary.totalExchanges} exchanges
                </Tag>
                <Tag icon={<DollarOutlined />} color="orange">
                  {formatCost(conversationSummary.totalCost)}
                </Tag>
              </div>
              
              <div style={{ display: 'flex', gap: 8, justifyContent: 'flex-end' }}>
                <Tag icon={<HistoryOutlined />} color="purple">
                  {conversationSummary.editCount || 0} edits
                </Tag>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  {formatDate(conversationSummary.lastModified)}
                </Text>
              </div>
            </Space>
          </div>
        )}
      </div>

      {/* Cost Comparison */}
      <div style={{ 
        marginTop: 12, 
        padding: 8, 
        backgroundColor: useOldConversation ? '#f0f8ff' : '#f6ffed',
        borderRadius: 4,
        border: `1px solid ${useOldConversation ? '#d6e4ff' : '#d9f7be'}`
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Text strong style={{ color: useOldConversation ? '#1890ff' : '#52c41a' }}>
              {useOldConversation ? '🎯 Premium Editing' : '💸 Budget Editing'}
            </Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {useOldConversation 
                ? 'Higher cost (~$0.15-$0.45) • Full context • Best quality'
                : 'Lower cost (~$0.03-$0.12) • No context • Good for simple edits'
              }
            </Text>
          </div>
          
          <div style={{ textAlign: 'right' }}>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              Estimated cost per edit
            </Text>
            <br />
            <Text strong style={{ 
              color: useOldConversation ? '#ff4d4f' : '#52c41a',
              fontSize: '14px'
            }}>
              {useOldConversation ? '$0.15-$0.45' : '$0.03-$0.12'}
            </Text>
          </div>
        </div>
      </div>

      {/* Smart Recommendations */}
      {conversationSummary && (
        <div style={{ marginTop: 8 }}>
          <SmartRecommendation 
            conversationSummary={conversationSummary}
            currentToggle={useOldConversation}
          />
        </div>
      )}
    </Card>
  );
};

// Smart recommendation component
const SmartRecommendation = ({ conversationSummary, currentToggle }) => {
  const getRecommendation = () => {
    const { totalExchanges, editCount, totalCost } = conversationSummary;
    
    // Recommend old conversation for complex projects
    if (totalExchanges > 10 && editCount < 3) {
      return {
        recommended: true,
        reason: 'Complex project with rich context - old conversation recommended',
        icon: '🧠'
      };
    }
    
    // Recommend new conversation for simple edits
    if (editCount > 5 || totalCost > 2) {
      return {
        recommended: false,
        reason: 'Multiple edits or high cost - consider fresh start for simple changes',
        icon: '💰'
      };
    }
    
    return null;
  };

  const recommendation = getRecommendation();
  
  if (!recommendation) return null;

  const isFollowingRecommendation = recommendation.recommended === currentToggle;

  return (
    <div style={{ 
      padding: 6,
      backgroundColor: isFollowingRecommendation ? '#f6ffed' : '#fff7e6',
      border: `1px solid ${isFollowingRecommendation ? '#d9f7be' : '#ffd591'}`,
      borderRadius: 4
    }}>
      <Text style={{ 
        fontSize: '11px',
        color: isFollowingRecommendation ? '#52c41a' : '#fa8c16'
      }}>
        {recommendation.icon} <strong>Smart Tip:</strong> {recommendation.reason}
        {!isFollowingRecommendation && ' (Consider switching toggle)'}
      </Text>
    </div>
  );
};

export default ConversationToggle;
