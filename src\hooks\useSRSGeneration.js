import { useState, useCallback } from 'react';
import { message } from 'antd';
import aiService from '../services/aiService';
import { useSRSHistory } from './useLocalStorage';

export const useSRSGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStep, setGenerationStep] = useState('');
  const [showClarificationQuestions, setShowClarificationQuestions] = useState(false);
  const [clarificationQuestions, setClarificationQuestions] = useState([]);
  const [clarificationAnswers, setClarificationAnswers] = useState({});
  const [generatedSRS, setGeneratedSRS] = useState(null);
  const [generatedTimeline, setGeneratedTimeline] = useState(null);
  const [error, setError] = useState(null);

  const { addToHistory } = useSRSHistory();

  // Simulate progress updates
  const updateProgress = useCallback((progress, step) => {
    setGenerationProgress(progress);
    setGenerationStep(step);
  }, []);

  // Generate clarification questions
  const generateClarificationQuestions = useCallback(async (formData) => {
    try {
      setIsGenerating(true);
      updateProgress(10, 'Analyzing requirements...');

      const questions = await aiService.generateClarificationQuestions(formData);

      // Parse questions (assuming they come as numbered list)
      const questionList = questions
        .split('\n')
        .filter(line => line.trim() && /^\d+\./.test(line.trim()))
        .map((line, index) => ({
          id: index + 1,
          question: line.replace(/^\d+\.\s*/, '').trim(),
          answer: ''
        }));

      setClarificationQuestions(questionList);
      setShowClarificationQuestions(true);
      updateProgress(20, 'Clarification questions generated');

      return questionList;
    } catch (err) {
      console.error('Error generating clarification questions:', err);
      setError(err.message);
      message.error('Failed to generate clarification questions');
      return [];
    } finally {
      setIsGenerating(false);
    }
  }, [updateProgress]);

  // Generate project timeline
  const generateTimeline = useCallback(async (formData) => {
    try {
      updateProgress(30, 'Generating project timeline...');

      const timeline = await aiService.generateTimeline(formData);
      setGeneratedTimeline(timeline);
      updateProgress(50, 'Project timeline generated');

      return timeline;
    } catch (err) {
      console.error('Error generating timeline:', err);
      setError(err.message);
      message.error('Failed to generate project timeline');
      return null;
    }
  }, [updateProgress]);



  // CHATGPT-STYLE INTERACTIVE SRS GENERATION
  // Step-by-Step Process with Question-Answer Flow
  const generateSRS = useCallback(async (formData, additionalAnswers = null) => {
    let response = null;
    let needsMoreInfoResult = false;

    try {
      setIsGenerating(true);
      setError(null);
      updateProgress(0, 'Initializing ChatGPT-style SRS generation...');

      // Check if AI is configured
      if (!aiService.isConfigured()) {
        throw new Error('AI service is not configured. Please check your API keys.');
      }

      // Set up progress tracking for interactive system
      aiService.updateProgress = updateProgress;

      // Execute ChatGPT-style interactive SRS generation
      updateProgress(5, 'Starting ChatGPT-style interactive SRS generation...');
      response = await aiService.generateSRS(formData, additionalAnswers);
      console.log(response, 'ChatGPT-style SRS Generation Response');

      // Check if more information is needed
      if (response.needsMoreInfo) {
        needsMoreInfoResult = true;
        setIsGenerating(false);
        updateProgress(50, 'Additional information required...');
        return {
          success: false,
          needsMoreInfo: true,
          questions: response.questions,
          message: response.message
        };
      }

      // If we have the final SRS document
      if (typeof response === 'string') {
        // Create SRS document
        updateProgress(95, 'Finalizing SRS document...');
        const srsDocument = {
          id: `srs_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          projectInfo: {
            name: formData.projectName,
            description: formData.projectDescription,
            mainGoal: formData.mainGoal
          },
          content: response,
          formData: formData,
          metadata: {
            generatedAt: new Date().toISOString(),
            aiProvider: aiService.getProviderInfo().primary,
            version: '1.0.0',
            documentType: 'ChatGPT-style Interactive SRS',
            generationMethod: 'Interactive Question-Answer System',
            wordCount: response.split(' ').length,
            estimatedReadTime: Math.ceil(response.split(' ').length / 200), // 200 words per minute
            qualityLevel: 'Project-Specific Professional Grade',
            complianceStandards: ['IEEE 830-1998', 'Industry Best Practices']
          }
        };

        // Save to history
        addToHistory(srsDocument);

        updateProgress(100, 'ChatGPT-style SRS generation completed successfully!');
        setGeneratedSRS(srsDocument);

        message.success('Project-specific SRS document generated successfully!');

        return {
          success: true,
          document: srsDocument
        };
      }

      throw new Error('Unexpected response format from AI service');

    } catch (err) {
      console.error('ChatGPT-style SRS Generation Error:', err);
      setError(err.message);
      message.error(`Failed to generate SRS: ${err.message}`);

      return {
        success: false,
        error: err.message
      };
    } finally {
      if (!needsMoreInfoResult) {
        setIsGenerating(false);
        // Reset progress after a delay
        setTimeout(() => {
          setGenerationProgress(0);
          setGenerationStep('');
        }, 3000);
      }
    }
  }, [addToHistory, updateProgress]);

  // Handle clarification answers
  const handleClarificationAnswer = useCallback((questionId, answer) => {
    setClarificationAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  }, []);

  // Skip clarification questions
  const skipClarificationQuestions = useCallback(() => {
    setShowClarificationQuestions(false);
    setClarificationQuestions([]);
    setClarificationAnswers({});
  }, []);

  // Continue with clarification answers
  const continueWithClarifications = useCallback(async (formData) => {
    setShowClarificationQuestions(false);

    // Continue SRS generation with answers
    return await generateSRS(formData, true, false); // Don't ask clarifications again
  }, [generateSRS]);

  // Reset generation state
  const resetGeneration = useCallback(() => {
    setIsGenerating(false);
    setGenerationProgress(0);
    setGenerationStep('');
    setShowClarificationQuestions(false);
    setClarificationQuestions([]);
    setClarificationAnswers({});
    setGeneratedSRS(null);
    setGeneratedTimeline(null);
    setError(null);
  }, []);

  return {
    // State
    isGenerating,
    generationProgress,
    generationStep,
    showClarificationQuestions,
    clarificationQuestions,
    clarificationAnswers,
    generatedSRS,
    generatedTimeline,
    error,

    // Actions
    generateSRS,
    generateClarificationQuestions,
    generateTimeline,
    handleClarificationAnswer,
    skipClarificationQuestions,
    continueWithClarifications,
    resetGeneration,

    // Utilities
    aiConfigured: aiService.isConfigured(),
    providerInfo: aiService.getProviderInfo()
  };
};
