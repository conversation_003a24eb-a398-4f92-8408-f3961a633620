import React from 'react';
import { Layout, Row, Col, Space, Divider } from 'antd';
import { FileText, Mail, Phone, MapPin, Github, Twitter, Linkedin } from 'lucide-react';
import { Link } from 'react-router-dom';

const { Footer: AntFooter } = Layout;

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <AntFooter className="bg-gray-900 text-gray-300 mt-auto">
      <div className="max-w-7xl mx-auto px-4 lg:px-8">
        <Row gutter={[32, 32]} className="py-12">
          {/* Company Info */}
          <Col xs={24} sm={12} lg={6}>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-primary-600 to-electric-500 rounded-lg flex items-center justify-center">
                  <FileText className="text-white" size={20} />
                </div>
                <div>
                  <h3 className="text-white font-bold text-lg">SRS Generator</h3>
                  <p className="text-sm text-gray-400">AI-Powered Documentation</p>
                </div>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed">
                Transform your ideas into professional Software Requirements Specification documents 
                with the power of AI technology.
              </p>
            </div>
          </Col>

          {/* Quick Links */}
          <Col xs={24} sm={12} lg={6}>
            <h4 className="text-white font-semibold mb-4">Quick Links</h4>
            <div className="space-y-2">
              <Link to="/" className="block text-gray-400 hover:text-white transition-colors">
                Home
              </Link>
              <Link to="/generate" className="block text-gray-400 hover:text-white transition-colors">
                Generate SRS
              </Link>
              <Link to="/history" className="block text-gray-400 hover:text-white transition-colors">
                History
              </Link>
              <Link to="/about" className="block text-gray-400 hover:text-white transition-colors">
                About Us
              </Link>
            </div>
          </Col>

          {/* Support */}
          <Col xs={24} sm={12} lg={6}>
            <h4 className="text-white font-semibold mb-4">Support</h4>
            <div className="space-y-2">
              <Link to="/contact" className="block text-gray-400 hover:text-white transition-colors">
                Contact Us
              </Link>
              <Link to="/terms" className="block text-gray-400 hover:text-white transition-colors">
                Terms & Conditions
              </Link>
              <a href="#" className="block text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="block text-gray-400 hover:text-white transition-colors">
                Help Center
              </a>
            </div>
          </Col>

          {/* Contact Info */}
          <Col xs={24} sm={12} lg={6}>
            <h4 className="text-white font-semibold mb-4">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail size={16} className="text-primary-400" />
                <span className="text-gray-400 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone size={16} className="text-primary-400" />
                <span className="text-gray-400 text-sm">+****************</span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin size={16} className="text-primary-400" />
                <span className="text-gray-400 text-sm">San Francisco, CA</span>
              </div>
            </div>
          </Col>
        </Row>

        <Divider className="border-gray-700" />

        {/* Bottom Section */}
        <div className="py-6 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p className="text-gray-400 text-sm">
            © {currentYear} SRS Generator. All rights reserved.
          </p>
          
          <Space size="large">
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <Github size={20} />
            </a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <Twitter size={20} />
            </a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <Linkedin size={20} />
            </a>
          </Space>
        </div>
      </div>
    </AntFooter>
  );
};

export default Footer;
