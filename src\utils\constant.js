import {
    FileText,
    Setting<PERSON>,
    Zap,
    Users,
    Target,
    Shield,
    Clock,
    Rocket,
} from "lucide-react";
import React from "react";

// Define steps with icons
export const srsSteps = [
    {
        title: "Basic Project Information",
        description: "Project name, description, and main goals",
        icon: React.createElement(FileText, { size: 20 }),
    },
    {
        title: "Platform & Deployment",
        description: "Target platforms and hosting preferences",
        icon: React.createElement(Settings, { size: 20 }),
    },
    {
        title: "Technology Stack",
        description: "Frontend, backend, and database technologies",
        icon: React.createElement(Zap, { size: 20 })
    },
    {
        title: "User Roles Definition",
        description: "Define user roles and their permissions",
        icon: React.createElement(Users, { size: 20 }),
    },
    {
        title: "Functional Modules",
        description: "Key features and module descriptions",
        icon: React.createElement(Target, { size: 20 })
    },
    {
        title: "Third-Party Integrations",
        description: "External services and APIs",
        icon: React.createElement(Shield, { size: 20 })
    },
    {
        title: "Development Lifecycle",
        description: "Methodology and collaboration tools",
        icon: React.createElement(Clock, { size: 20 })
    },
    {
        title: "Team Composition",
        description: "Team roles and experience levels",
        icon: React.createElement(Rocket, { size: 20 })
    },
];