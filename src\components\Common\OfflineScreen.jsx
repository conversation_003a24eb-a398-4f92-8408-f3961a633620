import React from 'react';
import { <PERSON>, Button, Typography, Space, Progress } from 'antd';
import { WifiOff, Refresh<PERSON>w, Alert<PERSON>riangle, Clock } from 'lucide-react';
import { motion } from 'framer-motion';
import { useNetwork, useOfflineQueue } from '../../hooks/useNetwork';

const { Title, Text, Paragraph } = Typography;

const OfflineScreen = ({ onRetry }) => {
  const { 
    isOnline, 
    connectionQuality, 
    offlineDuration, 
    pingTest,
    isSlowConnection 
  } = useNetwork();
  
  const { queue, queueSize, clearQueue } = useOfflineQueue();

  const formatDuration = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const handleRetry = async () => {
    const isConnected = await pingTest();
    if (isConnected && onRetry) {
      onRetry();
    }
  };

  if (isOnline && connectionQuality !== 'poor') {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-gray-900 bg-opacity-95 flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="max-w-md w-full"
      >
        <Card className="text-center shadow-2xl border-0">
          <div className="space-y-6">
            {/* Icon and Status */}
            <div className="space-y-4">
              <motion.div
                animate={{ 
                  rotate: isOnline ? 0 : [0, -10, 10, -10, 0],
                  scale: isOnline ? 1 : [1, 1.1, 1]
                }}
                transition={{ 
                  duration: isOnline ? 0.3 : 2,
                  repeat: isOnline ? 0 : Infinity,
                  repeatDelay: 1
                }}
                className={`w-20 h-20 mx-auto rounded-full flex items-center justify-center ${
                  isOnline 
                    ? 'bg-orange-100 text-orange-600' 
                    : 'bg-red-100 text-red-600'
                }`}
              >
                {isOnline ? (
                  <AlertTriangle size={40} />
                ) : (
                  <WifiOff size={40} />
                )}
              </motion.div>

              <div>
                <Title level={3} className="mb-2 text-gray-800">
                  {isOnline ? 'Slow Connection' : 'No Internet Connection'}
                </Title>
                <Text className="text-gray-600">
                  {isOnline 
                    ? 'Your connection is slow. Some features may not work properly.'
                    : 'Please check your internet connection and try again.'
                  }
                </Text>
              </div>
            </div>

            {/* Connection Details */}
            {!isOnline && (
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock size={16} className="text-gray-500" />
                    <Text className="text-sm text-gray-600">Offline for:</Text>
                  </div>
                  <Text className="text-sm font-medium text-gray-800">
                    {formatDuration(offlineDuration)}
                  </Text>
                </div>

                {queueSize > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Text className="text-sm text-gray-600">Queued actions:</Text>
                      <Text className="text-sm font-medium text-blue-600">
                        {queueSize}
                      </Text>
                    </div>
                    <Text className="text-xs text-gray-500">
                      These will be processed when connection is restored
                    </Text>
                  </div>
                )}
              </div>
            )}

            {/* Slow Connection Warning */}
            {isOnline && isSlowConnection && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle size={20} className="text-orange-600 mt-0.5" />
                  <div className="space-y-2">
                    <Text className="text-sm font-medium text-orange-800">
                      Slow Connection Detected
                    </Text>
                    <Text className="text-xs text-orange-700">
                      Consider switching to a faster network for better performance.
                      Some features may take longer to load.
                    </Text>
                  </div>
                </div>
              </div>
            )}

            {/* Troubleshooting Tips */}
            {!isOnline && (
              <div className="text-left space-y-3">
                <Text className="text-sm font-medium text-gray-700">
                  Troubleshooting tips:
                </Text>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Check your WiFi or mobile data connection</li>
                  <li>• Try moving closer to your router</li>
                  <li>• Restart your router or modem</li>
                  <li>• Check if other devices can connect</li>
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                type="primary"
                size="large"
                icon={<RefreshCw size={16} />}
                onClick={handleRetry}
                className="w-full btn-primary"
              >
                {isOnline ? 'Continue Anyway' : 'Try Again'}
              </Button>

              {!isOnline && queueSize > 0 && (
                <Button
                  size="large"
                  onClick={clearQueue}
                  className="w-full"
                  danger
                >
                  Clear Queued Actions ({queueSize})
                </Button>
              )}

              {isOnline && (
                <Text className="text-xs text-gray-500 block">
                  You can continue using the app, but some features may be limited
                </Text>
              )}
            </div>

            {/* Connection Quality Indicator */}
            {isOnline && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Text className="text-xs text-gray-500">Connection Quality:</Text>
                  <Text className={`text-xs font-medium ${
                    connectionQuality === 'excellent' ? 'text-green-600' :
                    connectionQuality === 'good' ? 'text-blue-600' :
                    connectionQuality === 'poor' ? 'text-orange-600' :
                    'text-red-600'
                  }`}>
                    {connectionQuality.charAt(0).toUpperCase() + connectionQuality.slice(1)}
                  </Text>
                </div>
                
                <Progress
                  percent={
                    connectionQuality === 'excellent' ? 100 :
                    connectionQuality === 'good' ? 75 :
                    connectionQuality === 'poor' ? 40 : 20
                  }
                  size="small"
                  strokeColor={
                    connectionQuality === 'excellent' ? '#10b981' :
                    connectionQuality === 'good' ? '#3b82f6' :
                    connectionQuality === 'poor' ? '#f59e0b' : '#ef4444'
                  }
                  showInfo={false}
                />
              </div>
            )}
          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default OfflineScreen;
