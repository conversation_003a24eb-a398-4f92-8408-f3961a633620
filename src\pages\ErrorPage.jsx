import React from "react";
import { <PERSON><PERSON>, <PERSON>po<PERSON>, Space, Card, Alert } from "antd";
import { Home, Refresh<PERSON>w, <PERSON>ert<PERSON><PERSON>gle, Bug, Mail } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";

const { Title, Text, Paragraph } = Typography;

const ErrorPage = ({
  error,
  errorInfo,
  onRetry,
  title = "Something went wrong",
  description = "We encountered an unexpected error. Our team has been notified and is working on a fix.",
}) => {
  // Safe navigation hook - only use if Router context is available
  let navigate = null;
  try {
    navigate = useNavigate();
  } catch {
    // Router context not available, will use window.location instead
    navigate = null;
  }

  const errorDetails =
    error?.stack || error?.message || "Unknown error occurred";
  const isNetworkError =
    error?.message?.includes("fetch") || error?.message?.includes("network");
  const isDevelopment = import.meta.env.DEV;

  const handleReportError = () => {
    const errorReport = {
      error: error?.message || "Unknown error",
      stack: error?.stack || "No stack trace available",
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    };

    // In a real app, you would send this to your error reporting service
    console.error("Error Report:", errorReport);

    // For demo, we'll just copy to clipboard
    navigator.clipboard
      .writeText(JSON.stringify(errorReport, null, 2))
      .then(() => alert("Error details copied to clipboard"))
      .catch(() => alert("Failed to copy error details"));
  };

  const handleRefresh = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  // Safe navigation function
  const safeNavigate = (path) => {
    if (navigate) {
      navigate(path);
    } else {
      window.location.href = path;
    }
  };

  // Safe Link component that works with or without Router context
  const SafeLink = ({ to, children, className, ...props }) => {
    if (navigate) {
      return (
        <Link to={to} className={className} {...props}>
          {children}
        </Link>
      );
    } else {
      return (
        <a
          href={to}
          className={className}
          onClick={(e) => {
            e.preventDefault();
            safeNavigate(to);
          }}
          {...props}
        >
          {children}
        </a>
      );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-6"
        >
          {/* Error Icon and Title */}
          <div className="text-center space-y-4">
            <motion.div
              animate={{
                rotate: [0, -5, 5, -5, 0],
                scale: [1, 1.05, 1],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 3,
              }}
              className="w-24 h-24 mx-auto bg-red-100 rounded-full flex items-center justify-center"
            >
              <AlertTriangle className="text-red-600" size={40} />
            </motion.div>

            <div>
              <Title level={2} className="text-gray-800 mb-2">
                {title}
              </Title>
              <Paragraph className="text-lg text-gray-600 max-w-lg mx-auto">
                {description}
              </Paragraph>
            </div>
          </div>

          {/* Error Type Alert */}
          {isNetworkError && (
            <Alert
              message="Network Error Detected"
              description="This error might be caused by a network connectivity issue. Please check your internet connection and try again."
              type="warning"
              showIcon
              className="mb-6"
            />
          )}

          {/* Main Error Card */}
          <Card className="shadow-lg">
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="text-center space-y-4">
                <Title level={4} className="text-gray-800">
                  What can you do?
                </Title>

                <Space size="large" wrap className="justify-center">
                  <Button
                    type="primary"
                    size="large"
                    icon={<RefreshCw size={16} />}
                    onClick={handleRefresh}
                    className="btn-primary"
                  >
                    Try Again
                  </Button>

                  <Button
                    size="large"
                    icon={<Home size={16} />}
                    className="btn-secondary"
                    onClick={() => safeNavigate("/")}
                  >
                    Go Home
                  </Button>
                </Space>
              </div>

              {/* Troubleshooting Steps */}
              <div className="bg-gray-50 rounded-lg p-4">
                <Title level={5} className="text-gray-800 mb-3">
                  Troubleshooting Steps:
                </Title>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                  <div>• Refresh the page</div>
                  <div>• Check your internet connection</div>
                  <div>• Clear your browser cache</div>
                  <div>• Try a different browser</div>
                  <div>• Disable browser extensions</div>
                  <div>• Contact support if issue persists</div>
                </div>
              </div>

              {/* Error Reporting */}
              <div className="border-t pt-4">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-3 md:space-y-0">
                  <div>
                    <Text className="text-sm text-gray-600">
                      Help us improve by reporting this error
                    </Text>
                  </div>
                  <Space>
                    <Button
                      size="small"
                      icon={<Bug size={14} />}
                      onClick={handleReportError}
                      className="text-gray-600 hover:text-gray-800"
                    >
                      Copy Error Details
                    </Button>
                    <Button
                      size="small"
                      icon={<Mail size={14} />}
                      className="text-blue-600 hover:text-blue-700"
                      onClick={() => safeNavigate("/contact")}
                    >
                      Contact Support
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          </Card>

          {/* Technical Details (Development Only) */}
          {isDevelopment && error && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <Card
                title={
                  <div className="flex items-center space-x-2">
                    <Bug size={16} className="text-red-600" />
                    <span>Technical Details (Development Mode)</span>
                  </div>
                }
                className="bg-red-50 border-red-200"
              >
                <div className="space-y-4">
                  <div>
                    <Text strong className="text-red-800">
                      Error Message:
                    </Text>
                    <div className="bg-white p-3 rounded border mt-2">
                      <Text code className="text-red-700 text-sm">
                        {error.message || "No error message available"}
                      </Text>
                    </div>
                  </div>

                  {error.stack && (
                    <div>
                      <Text strong className="text-red-800">
                        Stack Trace:
                      </Text>
                      <div className="bg-white p-3 rounded border mt-2 max-h-40 overflow-auto">
                        <pre className="text-xs text-red-700 whitespace-pre-wrap">
                          {error.stack}
                        </pre>
                      </div>
                    </div>
                  )}

                  {errorInfo && (
                    <div>
                      <Text strong className="text-red-800">
                        Component Stack:
                      </Text>
                      <div className="bg-white p-3 rounded border mt-2 max-h-40 overflow-auto">
                        <pre className="text-xs text-red-700 whitespace-pre-wrap">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          )}

          {/* Support Information */}
          <div className="text-center bg-blue-50 rounded-lg p-4">
            <Text className="text-sm text-blue-700">
              <strong>Need immediate help?</strong> Our support team is
              available 24/7. Contact us at{" "}
              <a
                href="mailto:<EMAIL>"
                className="font-medium underline"
              >
                <EMAIL>
              </a>{" "}
              or through our{" "}
              <SafeLink to="/contact" className="font-medium underline">
                contact page
              </SafeLink>
              .
            </Text>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ErrorPage;
