import { useState, useRef, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  Typography,
  Button,
  Space,
  Divider,
} from "antd";
import { Settings, Plus } from "lucide-react";
import { motion } from "framer-motion";
import {
  PLATFORM_OPTIONS,
  HOSTING_OPTIONS,
} from "../../../constants/srsFormData";

const { Title } = Typography;

const Step2PlatformDeployment = ({
  selectedPlatforms,
  setSelectedPlatforms,
  form,
}) => {
  const [platformOptions, setPlatformOptions] = useState([...PLATFORM_OPTIONS]);
  const [hostingOptions, setHostingOptions] = useState([...HOSTING_OPTIONS]);
  const [newPlatform, setNewPlatform] = useState("");
  const [newHosting, setNewHosting] = useState("");

  const platformInputRef = useRef(null);
  const hostingInputRef = useRef(null);

  // Add custom platform
  const addCustomPlatform = (e) => {
    e.preventDefault();
    if (newPlatform.trim() && !platformOptions.includes(newPlatform.trim())) {
      const updatedOptions = [...platformOptions, newPlatform.trim()];
      setPlatformOptions(updatedOptions);
      setNewPlatform("");
      setTimeout(() => {
        platformInputRef.current?.focus();
      }, 0);
    }
  };

  // Add custom hosting
  const addCustomHosting = (e) => {
    e.preventDefault();
    if (newHosting.trim() && !hostingOptions.includes(newHosting.trim())) {
      const updatedOptions = [...hostingOptions, newHosting.trim()];
      setHostingOptions(updatedOptions);
      setNewHosting("");
      setTimeout(() => {
        hostingInputRef.current?.focus();
      }, 0);
    }
  };

  // Sync form field with selectedPlatforms on mount and when selectedPlatforms changes
  useEffect(() => {
    const formPlatforms = form.getFieldValue("platforms") || [];
    if (JSON.stringify(formPlatforms) !== JSON.stringify(selectedPlatforms)) {
      form.setFieldValue("platforms", selectedPlatforms);
      console.log("🔄 Step2: Synced form platforms field:", selectedPlatforms);
    }
  }, [selectedPlatforms, form]);

  // Handle platform selection change
  const handlePlatformChange = (values) => {
    console.log("📝 Step2: Platform selection changed:", values);
    setSelectedPlatforms(values);
    form.setFieldValue("platforms", values);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="card shadow-lg">
        <Title level={3} className="mb-6 flex items-center text-gray-800">
          <Settings className="mr-3 text-blue-600" size={24} />
          Platform & Deployment Selection
        </Title>

        <Row gutter={[24, 24]}>
          <Col xs={24}>
            <Form.Item
              name="platforms"
              label="Platform Selection"
              rules={[
                {
                  required: true,
                  message: "Please select at least one platform",
                },
              ]}
            >
              <Select
                mode="multiple"
                placeholder="Select target platforms"
                size="large"
                className="w-full"
                value={selectedPlatforms}
                onChange={handlePlatformChange}
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
                popupRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: "8px 0" }} />
                    <Space style={{ padding: "0 8px 4px" }}>
                      <Input
                        placeholder="Add custom platform"
                        ref={platformInputRef}
                        value={newPlatform}
                        onChange={(e) => setNewPlatform(e.target.value)}
                        onKeyDown={(e) => {
                          e.stopPropagation();
                          if (e.key === "Enter") {
                            addCustomPlatform(e);
                          }
                        }}
                        style={{ width: 200 }}
                      />
                      <Button
                        type="text"
                        icon={<Plus size={14} />}
                        onClick={addCustomPlatform}
                        disabled={!newPlatform.trim()}
                      >
                        Add Platform
                      </Button>
                    </Space>
                  </>
                )}
                options={platformOptions.map((option) => ({
                  label: option,
                  value: option,
                }))}
              />
            </Form.Item>
          </Col>

          <Col xs={24}>
            <Form.Item
              name="hostingPreference"
              label="Hosting Preference"
              rules={[
                { required: true, message: "Please select hosting preference" },
              ]}
            >
              <Select
                mode="multiple"
                placeholder="Select hosting preferences"
                size="large"
                className="w-full"
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
                popupRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: "8px 0" }} />
                    <Space style={{ padding: "0 8px 4px" }}>
                      <Input
                        placeholder="Add custom hosting"
                        ref={hostingInputRef}
                        value={newHosting}
                        onChange={(e) => setNewHosting(e.target.value)}
                        onKeyDown={(e) => {
                          e.stopPropagation();
                          if (e.key === "Enter") {
                            addCustomHosting(e);
                          }
                        }}
                        style={{ width: 200 }}
                      />
                      <Button
                        type="text"
                        icon={<Plus size={14} />}
                        onClick={addCustomHosting}
                        disabled={!newHosting.trim()}
                      >
                        Add Hosting
                      </Button>
                    </Space>
                  </>
                )}
                options={hostingOptions.map((option) => ({
                  label: option,
                  value: option,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* Platform Selection Summary */}
        {selectedPlatforms.length > 0 && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <Title level={5} className="text-blue-800 mb-2">
              Selected Platforms Summary
            </Title>
            <div className="flex flex-wrap gap-2">
              {selectedPlatforms.map((platform) => (
                <span
                  key={platform}
                  className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium"
                >
                  {platform}
                </span>
              ))}
            </div>
            <p className="text-sm text-blue-600 mt-2">
              Technology options in the next step will be customized based on
              your platform selection.
            </p>
          </div>
        )}
      </Card>
    </motion.div>
  );
};

export default Step2PlatformDeployment;
