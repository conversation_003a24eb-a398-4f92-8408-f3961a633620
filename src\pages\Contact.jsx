import React from 'react';
import { Row, Col, Card, Form, Input, Button, message, Space } from 'antd';
import { Mail, Phone, MapPin, Clock, Send, MessageCircle } from 'lucide-react';
import { motion } from 'framer-motion';

const { TextArea } = Input;

const Contact = () => {
  const [form] = Form.useForm();

  const handleSubmit = (values) => {
    console.log('Contact form submitted:', values);
    message.success('Thank you for your message! We\'ll get back to you soon.');
    form.resetFields();
  };

  const contactInfo = [
    {
      icon: <Mail className="text-primary-600" size={24} />,
      title: 'Email Us',
      content: '<EMAIL>',
      description: 'Send us an email anytime',
    },
    {
      icon: <Phone className="text-electric-500" size={24} />,
      title: 'Call Us',
      content: '+****************',
      description: 'Mon-Fri from 8am to 6pm PST',
    },
    {
      icon: <MapPin className="text-cyan-500" size={24} />,
      title: 'Visit Us',
      content: '123 Tech Street, San Francisco, CA 94105',
      description: 'Come say hello at our office',
    },
    {
      icon: <Clock className="text-success-500" size={24} />,
      title: 'Business Hours',
      content: 'Monday - Friday: 8:00 AM - 6:00 PM PST',
      description: 'We\'re here to help during business hours',
    },
  ];

  const faqs = [
    {
      question: 'How does the AI-powered SRS generation work?',
      answer: 'Our AI analyzes your project inputs and generates IEEE 830 compliant documents using advanced natural language processing and machine learning algorithms.',
    },
    {
      question: 'What formats can I export my SRS documents to?',
      answer: 'You can export your generated SRS documents to PDF, Word (DOCX), and HTML formats for maximum compatibility.',
    },
    {
      question: 'Is my project data secure?',
      answer: 'Yes, we use enterprise-grade security measures including encryption at rest and in transit to protect your sensitive project information.',
    },
    {
      question: 'Can I collaborate with my team on SRS documents?',
      answer: 'Absolutely! Our platform supports team collaboration features including sharing, commenting, and version control.',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-electric-500 py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-white space-y-6"
          >
            <h1 className="text-4xl lg:text-6xl font-bold">
              Get in Touch
            </h1>
            <p className="text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto">
              Have questions about our SRS Generator? We're here to help you 
              streamline your documentation process.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Form & Info Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 lg:px-8">
          <Row gutter={[48, 48]}>
            {/* Contact Form */}
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Card className="card h-full">
                  <div className="space-y-6">
                    <div className="text-center">
                      <MessageCircle className="text-primary-600 mx-auto mb-4" size={48} />
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        Send us a Message
                      </h2>
                      <p className="text-gray-600">
                        Fill out the form below and we'll get back to you within 24 hours.
                      </p>
                    </div>

                    <Form
                      form={form}
                      layout="vertical"
                      onFinish={handleSubmit}
                      className="space-y-4"
                    >
                      <Row gutter={16}>
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="firstName"
                            label="First Name"
                            rules={[{ required: true, message: 'Please enter your first name' }]}
                          >
                            <Input 
                              placeholder="John"
                              className="form-input"
                            />
                          </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="lastName"
                            label="Last Name"
                            rules={[{ required: true, message: 'Please enter your last name' }]}
                          >
                            <Input 
                              placeholder="Doe"
                              className="form-input"
                            />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Form.Item
                        name="email"
                        label="Email Address"
                        rules={[
                          { required: true, message: 'Please enter your email' },
                          { type: 'email', message: 'Please enter a valid email' }
                        ]}
                      >
                        <Input 
                          placeholder="<EMAIL>"
                          className="form-input"
                        />
                      </Form.Item>

                      <Form.Item
                        name="company"
                        label="Company (Optional)"
                      >
                        <Input 
                          placeholder="Your Company Name"
                          className="form-input"
                        />
                      </Form.Item>

                      <Form.Item
                        name="subject"
                        label="Subject"
                        rules={[{ required: true, message: 'Please enter a subject' }]}
                      >
                        <Input 
                          placeholder="How can we help you?"
                          className="form-input"
                        />
                      </Form.Item>

                      <Form.Item
                        name="message"
                        label="Message"
                        rules={[{ required: true, message: 'Please enter your message' }]}
                      >
                        <TextArea 
                          rows={6}
                          placeholder="Tell us more about your inquiry..."
                          className="form-textarea"
                        />
                      </Form.Item>

                      <Form.Item>
                        <Button 
                          type="primary" 
                          htmlType="submit"
                          size="large"
                          className="w-full btn-primary"
                          icon={<Send size={16} />}
                        >
                          Send Message
                        </Button>
                      </Form.Item>
                    </Form>
                  </div>
                </Card>
              </motion.div>
            </Col>

            {/* Contact Information */}
            <Col xs={24} lg={12}>
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <div className="text-center lg:text-left">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Contact Information
                  </h2>
                  <p className="text-gray-600 mb-8">
                    Choose the best way to reach us. We're always happy to help!
                  </p>
                </div>

                <div className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Card className="card hover:shadow-lg transition-all duration-300">
                        <div className="flex items-start space-x-4">
                          <div className="w-12 h-12 bg-gray-50 rounded-lg flex items-center justify-center flex-shrink-0">
                            {info.icon}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 mb-1">
                              {info.title}
                            </h3>
                            <p className="text-gray-900 font-medium mb-1">
                              {info.content}
                            </p>
                            <p className="text-gray-600 text-sm">
                              {info.description}
                            </p>
                          </div>
                        </div>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </Col>
          </Row>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Quick answers to common questions about our SRS Generator platform.
            </p>
          </motion.div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="card">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-4xl mx-auto px-4 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-white">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Don't wait - start creating professional SRS documents today 
              and experience the power of AI-driven documentation.
            </p>
            <Space size="large" className="flex justify-center">
              <Button 
                type="primary" 
                size="large"
                className="h-12 px-8 text-lg font-semibold bg-gradient-to-r from-primary-600 to-electric-500 border-none"
              >
                Start Free Trial
              </Button>
              <Button 
                size="large"
                className="h-12 px-8 text-lg font-semibold bg-transparent text-white border-2 border-gray-600 hover:bg-gray-800"
              >
                Schedule Demo
              </Button>
            </Space>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
