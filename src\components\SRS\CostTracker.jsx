import React, { useState, useEffect } from 'react';
import { Card, Statistic, Row, Col, Progress, Typography, Space, Tag, Tooltip } from 'antd';
import { DollarOutlined, ThunderboltOutlined, MessageOutlined, EditOutlined } from '@ant-design/icons';
import conversationManager from '../../services/conversationManager';

const { Title, Text } = Typography;

const CostTracker = () => {
  const [costStats, setCostStats] = useState({
    totalProjects: 0,
    totalCost: 0,
    totalTokens: 0,
    totalEdits: 0,
    thisMonth: 0,
    avgCostPerProject: 0
  });

  const [monthlyBudget] = useState(50); // $50 monthly budget for development

  useEffect(() => {
    const updateStats = () => {
      const stats = conversationManager.getCostStatistics();
      setCostStats(stats);
    };

    updateStats();
    
    // Update stats every 30 seconds
    const interval = setInterval(updateStats, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const budgetUsagePercent = (costStats.thisMonth / monthlyBudget) * 100;
  const remainingBudget = monthlyBudget - costStats.thisMonth;

  const getBudgetColor = () => {
    if (budgetUsagePercent < 50) return '#52c41a';
    if (budgetUsagePercent < 80) return '#faad14';
    return '#ff4d4f';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <DollarOutlined style={{ color: '#1890ff' }} />
          <Title level={4} style={{ margin: 0 }}>
            💰 Development Cost Tracker
          </Title>
        </div>
      }
      style={{ marginBottom: 24 }}
    >
      {/* Monthly Budget Progress */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
          <Text strong>Monthly Budget Usage</Text>
          <Text type={budgetUsagePercent > 80 ? 'danger' : 'secondary'}>
            {formatCurrency(costStats.thisMonth)} / {formatCurrency(monthlyBudget)}
          </Text>
        </div>
        <Progress
          percent={Math.min(budgetUsagePercent, 100)}
          strokeColor={getBudgetColor()}
          trailColor="#f0f0f0"
          showInfo={false}
        />
        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 4 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {budgetUsagePercent.toFixed(1)}% used
          </Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {formatCurrency(remainingBudget)} remaining
          </Text>
        </div>
      </div>

      {/* Cost Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f6ffed' }}>
            <Statistic
              title="Total Projects"
              value={costStats.totalProjects}
              prefix={<MessageOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '18px' }}
            />
          </Card>
        </Col>
        
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center', backgroundColor: '#fff7e6' }}>
            <Statistic
              title="Total Cost"
              value={costStats.totalCost}
              prefix={<DollarOutlined style={{ color: '#fa8c16' }} />}
              precision={3}
              valueStyle={{ color: '#fa8c16', fontSize: '18px' }}
            />
          </Card>
        </Col>
        
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f0f8ff' }}>
            <Statistic
              title="Total Edits"
              value={costStats.totalEdits}
              prefix={<EditOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '18px' }}
            />
          </Card>
        </Col>
        
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center', backgroundColor: '#f9f0ff' }}>
            <Statistic
              title="Avg/Project"
              value={costStats.avgCostPerProject}
              prefix={<ThunderboltOutlined style={{ color: '#722ed1' }} />}
              precision={3}
              valueStyle={{ color: '#722ed1', fontSize: '18px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Cost Breakdown */}
      <div style={{ marginTop: 20 }}>
        <Title level={5} style={{ marginBottom: 12 }}>
          📊 Cost Breakdown & Savings
        </Title>
        
        <Space direction="vertical" style={{ width: '100%' }} size="small">
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            padding: '8px 12px',
            backgroundColor: '#f6ffed',
            borderRadius: 6,
            border: '1px solid #d9f7be'
          }}>
            <Text>💬 Context-Aware Edits (Premium)</Text>
            <Text strong style={{ color: '#52c41a' }}>
              ~{formatCurrency(costStats.totalCost * 0.7)} (70%)
            </Text>
          </div>
          
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            padding: '8px 12px',
            backgroundColor: '#f0f8ff',
            borderRadius: 6,
            border: '1px solid #d6e4ff'
          }}>
            <Text>🆕 Fresh Start Edits (Budget)</Text>
            <Text strong style={{ color: '#1890ff' }}>
              ~{formatCurrency(costStats.totalCost * 0.3)} (30%)
            </Text>
          </div>
        </Space>
      </div>

      {/* Token Usage */}
      <div style={{ marginTop: 16 }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          padding: '8px 12px',
          backgroundColor: '#fafafa',
          borderRadius: 6,
          border: '1px solid #d9d9d9'
        }}>
          <Space>
            <Text type="secondary">Total Tokens:</Text>
            <Tag color="purple">{formatNumber(costStats.totalTokens)}</Tag>
          </Space>
          <Tooltip title="Estimated based on 4 characters per token">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              ~{formatNumber(costStats.totalTokens * 4)} characters
            </Text>
          </Tooltip>
        </div>
      </div>

      {/* Budget Alerts */}
      {budgetUsagePercent > 80 && (
        <div style={{ 
          marginTop: 16,
          padding: 12,
          backgroundColor: budgetUsagePercent > 95 ? '#fff2f0' : '#fff7e6',
          border: `1px solid ${budgetUsagePercent > 95 ? '#ffccc7' : '#ffd591'}`,
          borderRadius: 6
        }}>
          <Text style={{ 
            color: budgetUsagePercent > 95 ? '#ff4d4f' : '#fa8c16',
            fontSize: '12px'
          }}>
            ⚠️ <strong>Budget Alert:</strong> You've used {budgetUsagePercent.toFixed(1)}% of your monthly budget. 
            {budgetUsagePercent > 95 
              ? ' Consider using more "Fresh Start" edits to save costs.'
              : ' Monitor your usage to stay within budget.'
            }
          </Text>
        </div>
      )}

      {/* Development Tips */}
      <div style={{ 
        marginTop: 16,
        padding: 12,
        backgroundColor: '#f6ffed',
        border: '1px solid #d9f7be',
        borderRadius: 6
      }}>
        <Text style={{ fontSize: '12px', color: '#52c41a' }}>
          💡 <strong>Cost-Saving Tips:</strong> Use "Fresh Start" editing for simple changes like typos or formatting. 
          Reserve "Context-Aware" editing for complex modifications that need conversation history.
        </Text>
      </div>
    </Card>
  );
};

export default CostTracker;
