import { useState, useCallback, useEffect } from "react";
import { Form, message } from "antd";
import { AnimatePresence, motion } from "framer-motion";

// Import AI generation components
import { useSRSGeneration } from "../hooks/useSRSGeneration";
import SRSDocumentViewer from "../components/SRS/SRSDocumentViewer";
import AdditionalQuestionsModal from "../components/AdditionalQuestionsModal";
import SaveProgressModal from "../components/SRS/SaveProgressModal";
import conversationManager from "../services/conversationManager";
import { useSRSHistory } from "../hooks/useLocalStorage";

// Sanitize data to remove circular references and React elements (MOVED OUTSIDE COMPONENT)
const sanitizeData = (data) => {
  try {
    return JSON.parse(
      JSON.stringify(data, (key, value) => {
        // Remove React elements and functions
        if (
          typeof value === "function" ||
          (value && value.$$typeof) ||
          (value && value._owner)
        ) {
          return undefined;
        }
        return value;
      })
    );
  } catch (error) {
    console.warn("Data sanitization failed:", error);
    return {};
  }
};

// Import step components
import Step1BasicInfo from "../components/SRS/Steps/Step1BasicInfo";
import Step2PlatformDeployment from "../components/SRS/Steps/Step2PlatformDeployment";
import Step3TechnologyStack from "../components/SRS/Steps/Step3TechnologyStack";
import Step4UserRoles from "../components/SRS/Steps/Step4UserRoles";
import Step5FunctionalModules from "../components/SRS/Steps/Step5FunctionalModules";
import Step6Integrations from "../components/SRS/Steps/Step6Integrations";
import Step7DevelopmentLifecycle from "../components/SRS/Steps/Step7DevelopmentLifecycle";
import Step8TeamComposition from "../components/SRS/Steps/Step8TeamComposition";

// Import navigation components
import ProgressSteps from "../components/SRS/ProgressSteps";
import NavigationButtons from "../components/SRS/NavigationButtons";

// SRS Steps Configuration
const srsSteps = [
  { title: "Basic Info", description: "Project fundamentals" },
  { title: "Platform", description: "Deployment & hosting" },
  { title: "Technology", description: "Tech stack selection" },
  { title: "User Roles", description: "System users & permissions" },
  { title: "Modules", description: "Functional requirements" },
  { title: "Integrations", description: "Third-party services" },
  { title: "Development", description: "Lifecycle & tools" },
  { title: "Team", description: "Project team structure" },
];

const Generate = () => {
  // Form instance
  const [form] = Form.useForm();

  // Step management
  const [currentStep, setCurrentStep] = useState(0);

  // Form data state management
  const [userRoles, setUserRoles] = useState([{ name: "", actions: "" }]);
  const [functionalModules, setFunctionalModules] = useState([
    { description: "", priority: "Medium" },
  ]);
  const [teamMembers, setTeamMembers] = useState({});
  const [selectedPlatforms, setSelectedPlatforms] = useState([]);

  // AI generation state
  const [showQuestionsModal, setShowQuestionsModal] = useState(false);
  const [additionalQuestions, setAdditionalQuestions] = useState([]);
  const [questionsMessage, setQuestionsMessage] = useState("");
  const [currentFormData, setCurrentFormData] = useState(null);

  // Save progress modal state
  const [showSaveProgressModal, setShowSaveProgressModal] = useState(false);
  const [isSavingProgress, setIsSavingProgress] = useState(false);

  // Use SRS history hook
  const { saveInProgress, completeInProgressSRS } = useSRSHistory();

  // Use AI generation hook
  const {
    isGenerating,
    generationProgress,
    generationStep,
    generatedSRS,
    generateSRS,
    resetGeneration,
    aiConfigured,
  } = useSRSGeneration();

  // GLOBAL SOLUTION: Centralized function to get ALL form data from all steps
  // OPTIMIZED to prevent circular references and memory issues
  const getAllFormData = useCallback(() => {
    try {
      // Get current form fields (only from mounted components)
      const currentFormFields = form.getFieldsValue();

      // Get saved form data from localStorage to include previous steps
      const savedFormData = localStorage.getItem("srs_form_data");
      let previousFormData = {};

      if (savedFormData) {
        try {
          const parsed = JSON.parse(savedFormData);
          // Only get the formData part, not the entire object to avoid circular refs
          previousFormData =
            parsed.formData || (parsed.projectName ? parsed : {});
        } catch (error) {
          console.warn("Failed to parse saved form data:", error);
          previousFormData = {};
        }
      }

      // Create clean data objects to prevent circular references
      const cleanUserRoles = userRoles.map((role) => ({
        name: role.name || "",
        actions: role.actions || "",
      }));

      const cleanFunctionalModules = functionalModules.map((module) => ({
        description: module.description || "",
        priority: module.priority || "Medium",
      }));

      const cleanTeamMembers = Object.keys(teamMembers).reduce((acc, key) => {
        acc[key] = teamMembers[key];
        return acc;
      }, {});

      const cleanSelectedPlatforms = Array.isArray(selectedPlatforms)
        ? [...selectedPlatforms]
        : [];

      // Combine all data sources for complete form data
      const completeFormData = {
        // Start with previous saved data
        ...previousFormData,
        // Override with current form fields
        ...currentFormFields,
        // Add clean state data
        userRoles: cleanUserRoles,
        functionalModules: cleanFunctionalModules,
        teamMembers: cleanTeamMembers,
        selectedPlatforms: cleanSelectedPlatforms,
        currentStep,
        savedAt: new Date().toISOString(),
      };

      // Only log in development to reduce console spam
      if (import.meta.env.DEV) {
        console.log("🔍 Complete form data collected:", {
          fieldsCount: Object.keys(completeFormData).length,
          currentStep,
          hasUserRoles: cleanUserRoles.length > 0,
          hasModules: cleanFunctionalModules.length > 0,
          hasPlatforms: cleanSelectedPlatforms.length > 0,
        });
      }

      return completeFormData;
    } catch (error) {
      console.error("❌ Error collecting complete form data:", error);
      // Fallback to minimal data to prevent crashes
      return {
        ...form.getFieldsValue(),
        currentStep,
        userRoles: [],
        functionalModules: [],
        teamMembers: {},
        selectedPlatforms: [],
        savedAt: new Date().toISOString(),
      };
    }
  }, [
    form,
    currentStep,
    userRoles,
    functionalModules,
    teamMembers,
    selectedPlatforms,
  ]);

  // CENTRALIZED SAVE FUNCTION - Called on both Next Step and Save Progress
  // OPTIMIZED to prevent memory issues and reduce localStorage size
  const saveFormDataToLocalStorage = useCallback(() => {
    try {
      // Use centralized function to get ALL form data from all steps
      const completeFormData = getAllFormData();

      // Create a lightweight version for localStorage
      const lightweightData = {
        // Core form fields
        projectName: completeFormData.projectName,
        projectDescription: completeFormData.projectDescription,
        mainGoal: completeFormData.mainGoal,
        platforms: completeFormData.platforms,
        hostingPreference: completeFormData.hostingPreference,
        // Technology fields
        frontendTech: completeFormData.frontendTech,
        backendTech: completeFormData.backendTech,
        database: completeFormData.database,
        // Other essential fields
        userRoles: completeFormData.userRoles,
        functionalModules: completeFormData.functionalModules,
        teamMembers: completeFormData.teamMembers,
        selectedPlatforms: completeFormData.selectedPlatforms,
        currentStep: completeFormData.currentStep,
        savedAt: completeFormData.savedAt,
      };

      // Remove undefined/null values to reduce size
      const cleanData = Object.fromEntries(
        Object.entries(lightweightData).filter(
          ([, value]) => value !== undefined && value !== null && value !== ""
        )
      );

      // Save to localStorage with size check
      const dataString = JSON.stringify(cleanData);
      if (dataString.length > 1024 * 1024) {
        // 1MB limit
        console.warn("⚠️ Form data is large, truncating...");
        // Keep only essential fields if data is too large
        const essentialData = {
          projectName: cleanData.projectName,
          currentStep: cleanData.currentStep,
          savedAt: cleanData.savedAt,
        };
        localStorage.setItem("srs_form_data", JSON.stringify(essentialData));
      } else {
        localStorage.setItem("srs_form_data", dataString);
      }

      if (import.meta.env.DEV) {
        console.log("💾 Form data saved to localStorage:", {
          step: currentStep,
          fieldsCount: Object.keys(cleanData).length,
          dataSize: `${Math.round(dataString.length / 1024)}KB`,
        });
      }

      return cleanData;
    } catch (error) {
      console.error("❌ Failed to save form data to localStorage:", error);
      // Try to save minimal data as fallback
      try {
        const minimalData = {
          currentStep,
          savedAt: new Date().toISOString(),
          projectName: form.getFieldValue("projectName") || "Untitled",
        };
        localStorage.setItem("srs_form_data", JSON.stringify(minimalData));
        return minimalData;
      } catch (fallbackError) {
        console.error("❌ Even minimal save failed:", fallbackError);
        return null;
      }
    }
  }, [getAllFormData, currentStep, form]);

  // Auto-save functionality (ENHANCED - Uses centralized save function)
  useEffect(() => {
    const interval = setInterval(() => {
      try {
        // Use centralized save function
        const savedData = saveFormDataToLocalStorage();
        if (savedData) {
          console.log("💾 Auto-save completed:", {
            step: currentStep,
            fieldsCount: Object.keys(savedData).length,
          });
        }
      } catch (error) {
        console.warn("Auto-save failed:", error);
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(interval);
  }, [saveFormDataToLocalStorage, currentStep]);

  // Memory cleanup on component mount
  useEffect(() => {
    // Clean up old localStorage data on component mount
    try {
      const keys = Object.keys(localStorage);
      const oldKeys = keys.filter(
        (key) =>
          key.startsWith("srs_") &&
          key !== "srs_form_data" &&
          key !== "srs_history"
      );

      // Remove old conversation data older than 7 days
      oldKeys.forEach((key) => {
        try {
          const data = JSON.parse(localStorage.getItem(key));
          if (data.lastModified) {
            const lastModified = new Date(data.lastModified);
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

            if (lastModified < sevenDaysAgo && data.status !== "completed") {
              localStorage.removeItem(key);
            }
          }
        } catch {
          // Remove corrupted entries
          localStorage.removeItem(key);
        }
      });

      if (import.meta.env.DEV) {
        console.log("🧹 Cleaned up old localStorage data");
      }
    } catch (error) {
      console.warn("Failed to cleanup old localStorage data:", error);
    }
  }, []);

  // Load saved data on component mount (ENHANCED - Check both storage systems)
  useEffect(() => {
    const loadFormData = () => {
      try {
        // Check srs_form_data first
        const savedFormData = localStorage.getItem("srs_form_data");

        // Check for in-progress SRS in history
        const historyData = JSON.parse(
          localStorage.getItem("srs_history") || "[]"
        );
        const inProgressSRS = historyData.find(
          (doc) => doc?.metadata?.status === "in-progress"
        );

        let dataToLoad = null;
        let dataSource = "";

        // Determine which data to use (prioritize most recent)
        if (savedFormData && inProgressSRS) {
          const formDataParsed = JSON.parse(savedFormData);
          const formDataTime = new Date(formDataParsed.savedAt || 0).getTime();
          const progressTime = new Date(
            inProgressSRS.progressData?.savedAt || 0
          ).getTime();

          if (progressTime > formDataTime) {
            // Use in-progress data (more recent)
            dataToLoad = {
              ...inProgressSRS.progressData.formData,
              currentStep: inProgressSRS.progressData.currentStep,
              userRoles: inProgressSRS.progressData.userRoles,
              functionalModules: inProgressSRS.progressData.functionalModules,
              teamMembers: inProgressSRS.progressData.teamMembers,
              selectedPlatforms: inProgressSRS.progressData.selectedPlatforms,
              resumingFromHistory: true,
            };
            dataSource = "in-progress SRS (more recent)";
          } else {
            // Use form data (more recent)
            dataToLoad = formDataParsed;
            dataSource = "srs_form_data (more recent)";
          }
        } else if (savedFormData) {
          dataToLoad = JSON.parse(savedFormData);
          dataSource = "srs_form_data only";
        } else if (inProgressSRS) {
          dataToLoad = {
            ...inProgressSRS.progressData.formData,
            currentStep: inProgressSRS.progressData.currentStep,
            userRoles: inProgressSRS.progressData.userRoles,
            functionalModules: inProgressSRS.progressData.functionalModules,
            teamMembers: inProgressSRS.progressData.teamMembers,
            selectedPlatforms: inProgressSRS.progressData.selectedPlatforms,
            resumingFromHistory: true,
          };
          dataSource = "in-progress SRS only";
        }

        if (dataToLoad) {
          console.log(`📥 Loading saved data from: ${dataSource}`, dataToLoad);

          // Set form fields - handle both old and new format
          const formData = dataToLoad.formData || dataToLoad;
          if (formData && typeof formData === "object") {
            // Ensure platforms field is synchronized with selectedPlatforms
            const formDataWithPlatforms = {
              ...formData,
              platforms:
                dataToLoad.selectedPlatforms || formData.platforms || [],
            };
            form.setFieldsValue(formDataWithPlatforms);
            console.log(
              "✅ Form fields restored with synchronized platforms:",
              formDataWithPlatforms
            );
          }

          // Set current step
          const stepToSet =
            dataToLoad.currentStep !== undefined ? dataToLoad.currentStep : 0;
          setCurrentStep(stepToSet);
          console.log("✅ Current step restored:", stepToSet);

          // Set user roles
          const rolesToSet =
            dataToLoad.userRoles &&
            Array.isArray(dataToLoad.userRoles) &&
            dataToLoad.userRoles.length > 0
              ? dataToLoad.userRoles
              : [{ name: "", actions: "" }];
          setUserRoles(rolesToSet);
          console.log("✅ User roles restored:", rolesToSet);

          // Set functional modules
          const modulesToSet =
            dataToLoad.functionalModules &&
            Array.isArray(dataToLoad.functionalModules) &&
            dataToLoad.functionalModules.length > 0
              ? dataToLoad.functionalModules
              : [{ description: "", priority: "Medium" }];
          setFunctionalModules(modulesToSet);
          console.log("✅ Functional modules restored:", modulesToSet);

          // Set team members
          const teamToSet =
            dataToLoad.teamMembers && typeof dataToLoad.teamMembers === "object"
              ? dataToLoad.teamMembers
              : {};
          setTeamMembers(teamToSet);
          console.log("✅ Team members restored:", teamToSet);

          // Set selected platforms
          const platformsToSet =
            dataToLoad.selectedPlatforms &&
            Array.isArray(dataToLoad.selectedPlatforms)
              ? dataToLoad.selectedPlatforms
              : [];
          setSelectedPlatforms(platformsToSet);
          console.log("✅ Selected platforms restored:", platformsToSet);

          // If resuming from history, show message and clear the flag
          if (dataToLoad.resumingFromHistory) {
            message.info(`Resuming SRS generation from step ${stepToSet + 1}`);
            // Don't clear localStorage immediately, let user navigate first
          }

          // Sync both storage systems to ensure consistency
          if (dataSource.includes("in-progress")) {
            // Update srs_form_data to match in-progress data
            try {
              const sanitizedData = sanitizeData(dataToLoad);
              localStorage.setItem(
                "srs_form_data",
                JSON.stringify(sanitizedData)
              );
              console.log("✅ Synced srs_form_data with in-progress data");
            } catch (error) {
              console.warn("⚠️ Failed to sync srs_form_data:", error);
            }
          }
        }
      } catch (error) {
        console.error("❌ Error loading saved data:", error);
        localStorage.removeItem("srs_form_data");
      }
    };

    loadFormData();
  }, [form]);

  // Sync selectedPlatforms with form field (CRITICAL FIX)
  useEffect(() => {
    const currentPlatforms = form.getFieldValue("platforms") || [];
    if (
      JSON.stringify(currentPlatforms) !== JSON.stringify(selectedPlatforms)
    ) {
      form.setFieldValue("platforms", selectedPlatforms);
      console.log(
        "🔄 Synced platforms field with selectedPlatforms:",
        selectedPlatforms
      );
    }
  }, [selectedPlatforms, form]);

  // Check for existing in-progress SRS on mount (FIXED - No dependencies to prevent loops)
  useEffect(() => {
    // Check localStorage directly to avoid dependency loops
    const checkInProgressSRS = () => {
      try {
        const historyData = JSON.parse(
          localStorage.getItem("srs_history") || "[]"
        );
        const inProgressSRS = historyData.find(
          (doc) => doc?.metadata?.status === "in-progress"
        );

        if (inProgressSRS && window.location.pathname === "/generate") {
          // If user navigated directly to generate page and there's in-progress SRS
          // We could auto-load it or show a notification
          console.log("Found in-progress SRS:", inProgressSRS.projectInfo.name);
        }
      } catch (error) {
        console.error("Error checking in-progress SRS:", error);
      }
    };

    checkInProgressSRS();
  }, []); // Empty dependency array to run only once on mount

  // Component cleanup to prevent memory leaks
  useEffect(() => {
    return () => {
      // Cleanup on component unmount
      try {
        // Clear any pending timeouts or intervals
        if (window.srsGenerationTimeout) {
          clearTimeout(window.srsGenerationTimeout);
        }

        // Force garbage collection if available
        if (window.gc) {
          window.gc();
        }

        // Clear large state objects
        setCurrentFormData(null);
        setAdditionalQuestions([]);

        if (import.meta.env.DEV) {
          console.log("🧹 Generate component cleanup completed");
        }
      } catch (error) {
        console.warn("Cleanup error:", error);
      }
    };
  }, []);

  // Memoized helper functions for better performance
  const addUserRole = useCallback(() => {
    setUserRoles((prev) => [...prev, { name: "", actions: "" }]);
  }, []);

  const removeUserRole = useCallback((index) => {
    setUserRoles((prev) => {
      if (prev.length > 1) {
        return prev.filter((_, i) => i !== index);
      }
      return prev;
    });
  }, []);

  const updateUserRole = useCallback((index, field, value) => {
    setUserRoles((prev) => {
      const newRoles = [...prev];
      newRoles[index][field] = value;
      return newRoles;
    });
  }, []);

  const addFunctionalModule = useCallback(() => {
    setFunctionalModules((prev) => [
      ...prev,
      { description: "", priority: "Medium" },
    ]);
  }, []);

  const removeFunctionalModule = useCallback((index) => {
    setFunctionalModules((prev) => {
      if (prev.length > 1) {
        return prev.filter((_, i) => i !== index);
      }
      return prev;
    });
  }, []);

  const updateFunctionalModule = useCallback((index, field, value) => {
    setFunctionalModules((prev) => {
      const newModules = [...prev];
      newModules[index][field] = value;
      return newModules;
    });
  }, []);

  const handleGenerateSRS = useCallback(
    async (additionalAnswers = null) => {
      // Prevent multiple simultaneous generations
      if (isGenerating) {
        console.warn(
          "SRS generation already in progress, skipping duplicate call"
        );
        return;
      }

      try {
        // Use centralized function to get ALL form data from all steps
        const completeFormData = getAllFormData();

        // Create clean form data to prevent circular references
        const formData = {
          // Core project info
          projectName: completeFormData.projectName,
          projectDescription: completeFormData.projectDescription,
          mainGoal: completeFormData.mainGoal,
          // Platform and tech data
          selectedPlatforms: Array.isArray(selectedPlatforms)
            ? [...selectedPlatforms]
            : [],
          frontendTech: completeFormData.frontendTech,
          backendTech: completeFormData.backendTech,
          database: completeFormData.database,
          // User and module data (clean copies)
          userRoles: userRoles.map((role) => ({
            name: role.name || "",
            actions: role.actions || "",
          })),
          functionalModules: functionalModules.map((module) => ({
            description: module.description || "",
            priority: module.priority || "Medium",
          })),
          teamMembers: { ...teamMembers },
          // Other fields
          hostingPreference: completeFormData.hostingPreference,
          developmentApproach: completeFormData.developmentApproach,
          integrations: completeFormData.integrations,
        };

        // Store current form data for potential additional questions (lightweight)
        setCurrentFormData({
          projectName: formData.projectName,
          projectId: formData.projectId,
          currentStep,
        });

        // Check if AI is configured
        if (!aiConfigured) {
          message.error(
            "AI service is not configured. Please check your API keys in the environment variables."
          );
          return;
        }

        // Start ChatGPT-style interactive SRS generation
        const result = await generateSRS(formData, additionalAnswers);
        console.log(result, "result");

        if (result.success) {
          message.success("SRS document generated successfully!");
          // Document viewer will be shown automatically
          setShowQuestionsModal(false);
          // Clear form data from memory to free up space
          setCurrentFormData(null);
        } else if (result.needsMoreInfo) {
          // Show additional questions modal
          setAdditionalQuestions(result.questions);
          setQuestionsMessage(result.message);
          setShowQuestionsModal(true);
          // Store project ID for continuation
          if (result.projectId) {
            setCurrentFormData((prev) => ({
              ...prev,
              projectId: result.projectId,
            }));
          }
          message.info(
            "Additional information required for comprehensive SRS generation"
          );
        } else {
          message.error(`Failed to generate SRS: ${result.error}`);
          setCurrentFormData(null); // Clear on error
        }
      } catch (error) {
        console.error("Generation error:", error);
        message.error("Failed to generate SRS document. Please try again.");
        setCurrentFormData(null); // Clear on error
      }
    },
    [
      getAllFormData,
      userRoles,
      functionalModules,
      teamMembers,
      selectedPlatforms,
      generateSRS,
      aiConfigured,
      isGenerating,
      currentStep,
    ]
  );

  // Handle additional questions submission
  const handleAdditionalQuestionsSubmit = useCallback(
    async (answers) => {
      try {
        // Enhanced sanitization to prevent circular JSON issues
        const sanitizedAnswers = sanitizeData(answers);

        // Check if user chose to skip questions
        if (sanitizedAnswers.skipQuestions && sanitizedAnswers.autoAnalyze) {
          console.log(
            "🤖 User chose to skip questions - auto-analyzing project data"
          );
          await handleGenerateSRS(sanitizedAnswers);
          return;
        }

        // Additional safety check - ensure no circular references
        const testSerialization = JSON.stringify(sanitizedAnswers);
        if (testSerialization.length > 100000) {
          // 100KB limit
          console.warn("⚠️ Large answer data detected, truncating...");
          // Keep only essential fields if data is too large
          const essentialAnswers = Object.fromEntries(
            Object.entries(sanitizedAnswers).slice(0, 10) // First 10 answers only
          );
          await handleGenerateSRS(essentialAnswers);
        } else {
          await handleGenerateSRS(sanitizedAnswers);
        }
      } catch (error) {
        console.error("❌ Error processing additional answers:", error);
        // Fallback with minimal data
        await handleGenerateSRS({
          fallback: "Additional answers provided but could not be processed",
        });
      }
    },
    [handleGenerateSRS]
  );

  // Handle modal close
  const handleModalClose = useCallback(() => {
    setShowQuestionsModal(false);
    setAdditionalQuestions([]);
    setQuestionsMessage("");
  }, []);

  // Handle save progress (fixed)
  const handleSaveProgress = useCallback(
    async (progressData) => {
      // Prevent multiple simultaneous saves
      if (isSavingProgress) {
        console.warn("Save already in progress, skipping duplicate call");
        return null;
      }

      try {
        setIsSavingProgress(true);

        // SAVE FORM DATA TO LOCALSTORAGE FIRST
        const savedFormData = saveFormDataToLocalStorage();
        if (!savedFormData) {
          throw new Error("Failed to save form data to localStorage");
        }

        // Create complete progress data using the saved form data
        const completeProgressData = {
          formData: savedFormData,
          currentStep: currentStep,
          userRoles: userRoles,
          functionalModules: functionalModules,
          teamMembers: teamMembers,
          selectedPlatforms: selectedPlatforms,
          savedAt: new Date().toISOString(),
          ...progressData,
        };

        console.log("💾 Saving progress data:", completeProgressData);

        const savedDocument = saveInProgress(completeProgressData);

        if (savedDocument) {
          message.success(
            `Progress saved! You can continue from step ${
              currentStep + 1
            } later.`
          );

          // IMPORTANT: Update srs_form_data to match saved progress for consistency
          try {
            const formDataForLocalStorage = {
              ...completeProgressData.formData,
              currentStep: completeProgressData.currentStep,
              userRoles: completeProgressData.userRoles,
              functionalModules: completeProgressData.functionalModules,
              teamMembers: completeProgressData.teamMembers,
              selectedPlatforms: completeProgressData.selectedPlatforms,
              savedAt: completeProgressData.savedAt,
              progressSaved: true, // Flag to indicate this was saved
            };

            const sanitizedFormData = sanitizeData(formDataForLocalStorage);
            localStorage.setItem(
              "srs_form_data",
              JSON.stringify(sanitizedFormData)
            );
            console.log("✅ Updated srs_form_data to match saved progress");
          } catch (error) {
            console.warn("⚠️ Failed to update srs_form_data:", error);
          }

          return savedDocument;
        } else {
          throw new Error("Failed to save progress document");
        }
      } catch (error) {
        console.error("❌ Save progress error:", error);
        message.error(`Failed to save progress: ${error.message}`);
        return null;
      } finally {
        setIsSavingProgress(false);
      }
    },
    [
      saveInProgress,
      isSavingProgress,
      saveFormDataToLocalStorage,
      currentStep,
      userRoles,
      functionalModules,
      teamMembers,
      selectedPlatforms,
    ]
  );

  // Handle save progress modal actions
  const handleSaveAndStartNew = useCallback(async () => {
    // SAVE FORM DATA TO LOCALSTORAGE FIRST
    const savedFormData = saveFormDataToLocalStorage();
    if (!savedFormData) {
      message.error("Failed to save current progress");
      return;
    }

    const progressData = {
      formData: savedFormData,
      currentStep,
      userRoles,
      functionalModules,
      teamMembers,
      selectedPlatforms,
    };

    await handleSaveProgress(progressData);

    // Reset form for new SRS
    form.resetFields();
    setCurrentStep(0);
    setUserRoles([{ name: "", actions: "" }]);
    setFunctionalModules([{ description: "", priority: "Medium" }]);
    setTeamMembers({});
    setSelectedPlatforms([]);

    setShowSaveProgressModal(false);
    message.info(
      "Starting new SRS generation. Previous progress saved to history."
    );
  }, [
    form,
    saveFormDataToLocalStorage,
    currentStep,
    userRoles,
    functionalModules,
    teamMembers,
    selectedPlatforms,
    handleSaveProgress,
  ]);

  const handleContinueCurrent = useCallback(() => {
    setShowSaveProgressModal(false);
    message.info("Continuing with current SRS generation.");
  }, []);

  const handleDiscardAndStartNew = useCallback(() => {
    // Reset form for new SRS
    form.resetFields();
    setCurrentStep(0);
    setUserRoles([{ name: "", actions: "" }]);
    setFunctionalModules([{ description: "", priority: "Medium" }]);
    setTeamMembers({});
    setSelectedPlatforms([]);

    // Clear localStorage
    localStorage.removeItem("srs_form_data");

    setShowSaveProgressModal(false);
    message.warning(
      "Previous progress discarded. Starting fresh SRS generation."
    );
  }, [form]);

  // Memoized handler functions for better performance
  const handleNext = useCallback(async () => {
    try {
      await form.validateFields();

      // SAVE FORM DATA TO LOCALSTORAGE ON NEXT STEP
      const savedData = saveFormDataToLocalStorage();
      if (savedData) {
        console.log("✅ Step data saved before navigation:", {
          fromStep: currentStep,
          toStep: currentStep + 1,
          savedFields: Object.keys(savedData),
        });
      }

      if (currentStep < srsSteps.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        // Generate SRS
        handleGenerateSRS();
      }
    } catch {
      message.error("Please fill in all required fields before proceeding.");
    }
  }, [currentStep, form, handleGenerateSRS, saveFormDataToLocalStorage]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // Render current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return <Step1BasicInfo form={form} />;
      case 1:
        return (
          <Step2PlatformDeployment
            selectedPlatforms={selectedPlatforms}
            setSelectedPlatforms={setSelectedPlatforms}
            form={form}
          />
        );
      case 2:
        return (
          <Step3TechnologyStack
            selectedPlatforms={selectedPlatforms}
            form={form}
          />
        );
      case 3:
        return (
          <Step4UserRoles
            userRoles={userRoles}
            addUserRole={addUserRole}
            removeUserRole={removeUserRole}
            updateUserRole={updateUserRole}
            form={form}
          />
        );
      case 4:
        return (
          <Step5FunctionalModules
            functionalModules={functionalModules}
            addFunctionalModule={addFunctionalModule}
            removeFunctionalModule={removeFunctionalModule}
            updateFunctionalModule={updateFunctionalModule}
            form={form}
          />
        );
      case 5:
        return <Step6Integrations form={form} />;
      case 6:
        return <Step7DevelopmentLifecycle form={form} />;
      case 7:
        return (
          <Step8TeamComposition
            teamMembers={teamMembers}
            setTeamMembers={setTeamMembers}
            form={form}
          />
        );
      default:
        return <Step1BasicInfo form={form} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Enhanced Progress Steps Component */}
          <ProgressSteps steps={srsSteps} currentStep={currentStep} />

          {/* Form Content */}
          <Form form={form} layout="vertical" className="mt-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderCurrentStep()}
              </motion.div>
            </AnimatePresence>
          </Form>

          {/* Enhanced Navigation Buttons Component */}
          <div className="mt-8">
            <NavigationButtons
              currentStep={currentStep}
              totalSteps={srsSteps?.length}
              isGenerating={isGenerating}
              generationProgress={generationProgress}
              generationStep={generationStep}
              onPrevious={handlePrevious}
              onNext={handleNext}
              onGenerate={handleGenerateSRS}
              onSave={() => message.success("Progress saved successfully!")}
              onSaveProgress={handleSaveProgress}
              getAllFormData={getAllFormData}
              form={form}
              userRoles={userRoles}
              functionalModules={functionalModules}
              teamMembers={teamMembers}
              selectedPlatforms={selectedPlatforms}
              aiConfigured={aiConfigured}
            />
          </div>
        </motion.div>
      </div>

      {/* SRS Document Viewer */}
      {generatedSRS && (
        <SRSDocumentViewer
          document={generatedSRS}
          onSave={() => {
            // Handle document save
            message.success("Document saved successfully!");
          }}
          onClose={() => {
            resetGeneration();
          }}
          editable={true}
        />
      )}

      {/* ChatGPT-style Additional Questions Modal */}
      {showQuestionsModal && (
        <AdditionalQuestionsModal
          visible={showQuestionsModal}
          onCancel={handleModalClose}
          onSubmit={handleAdditionalQuestionsSubmit}
          questions={additionalQuestions}
          message={questionsMessage}
          loading={isGenerating}
        />
      )}

      {/* Save Progress Modal */}
      <SaveProgressModal
        visible={showSaveProgressModal}
        onCancel={() => setShowSaveProgressModal(false)}
        onSaveAndStartNew={handleSaveAndStartNew}
        onContinueCurrent={handleContinueCurrent}
        onDiscardAndStartNew={handleDiscardAndStartNew}
        currentStep={currentStep}
        totalSteps={srsSteps?.length || 8}
        projectName={form.getFieldValue("projectName") || "Untitled Project"}
        loading={isSavingProgress}
      />
    </div>
  );
};

export default Generate;
