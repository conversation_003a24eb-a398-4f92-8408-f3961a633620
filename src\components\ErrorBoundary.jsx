import React from 'react';
import { Result, But<PERSON> } from 'antd';
import { AlertTriangle } from 'lucide-react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details for debugging
    const errorDetails = {
      error: error.toString(),
      errorInfo: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId
    };

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Caught Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Error Details:', errorDetails);
      console.groupEnd();
    }

    // Store error details in state
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // In production, you would send this to your error reporting service
    // Example: Sentry, LogRocket, etc.
    // this.reportError(errorDetails);
  }

  reportError = (errorDetails) => {
    // Future: Send to error reporting service
    try {
      // Store in localStorage for now (in production, send to API)
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorDetails);
      
      // Keep only last 10 errors
      const recentErrors = existingErrors.slice(-10);
      localStorage.setItem('app_errors', JSON.stringify(recentErrors));
    } catch (err) {
      console.error('Failed to store error details:', err);
    }
  };

  handleReload = () => {
    // Clear error state and reload
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    });
    window.location.reload();
  };

  handleGoHome = () => {
    // Clear error state and navigate to home
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null 
    });
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Custom error UI
      return (
        <div style={{ 
          minHeight: '100vh', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
        }}>
          <div style={{ 
            maxWidth: 600, 
            padding: 40,
            background: 'white',
            borderRadius: 12,
            boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
          }}>
            <Result
              icon={<AlertTriangle size={64} color="#ff4d4f" />}
              title="Oops! Something went wrong"
              subTitle={
                <div>
                  <p style={{ marginBottom: 16, color: '#666' }}>
                    We encountered an unexpected error. Don't worry, your data is safe.
                  </p>
                  {this.state.errorId && (
                    <p style={{ 
                      fontSize: '12px', 
                      color: '#999',
                      fontFamily: 'monospace',
                      background: '#f5f5f5',
                      padding: '8px 12px',
                      borderRadius: 4,
                      marginBottom: 16
                    }}>
                      Error ID: {this.state.errorId}
                    </p>
                  )}
                  {process.env.NODE_ENV === 'development' && this.state.error && (
                    <details style={{ 
                      marginTop: 16,
                      padding: 12,
                      background: '#fff2f0',
                      border: '1px solid #ffccc7',
                      borderRadius: 4,
                      fontSize: '12px'
                    }}>
                      <summary style={{ cursor: 'pointer', fontWeight: 'bold', color: '#ff4d4f' }}>
                        🔍 Developer Details (Click to expand)
                      </summary>
                      <div style={{ marginTop: 8, fontFamily: 'monospace' }}>
                        <strong>Error:</strong>
                        <pre style={{ whiteSpace: 'pre-wrap', margin: '8px 0' }}>
                          {this.state.error.toString()}
                        </pre>
                        <strong>Component Stack:</strong>
                        <pre style={{ whiteSpace: 'pre-wrap', margin: '8px 0' }}>
                          {this.state.errorInfo?.componentStack}
                        </pre>
                      </div>
                    </details>
                  )}
                </div>
              }
              extra={[
                <Button 
                  type="primary" 
                  key="reload" 
                  onClick={this.handleReload}
                  style={{ marginRight: 8 }}
                >
                  🔄 Reload Page
                </Button>,
                <Button 
                  key="home" 
                  onClick={this.handleGoHome}
                >
                  🏠 Go Home
                </Button>
              ]}
            />
          </div>
        </div>
      );
    }

    // Render children normally if no error
    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default ErrorBoundary;
