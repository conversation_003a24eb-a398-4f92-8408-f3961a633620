import {
  <PERSON><PERSON>,
  Card,
  Input,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Divider,
} from "antd";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowRight, Lightbulb } from "lucide-react";
import { motion } from "framer-motion";

const { Title, Text } = Typography;
const { TextArea } = Input;

const ClarificationQuestions = ({
  visible,
  questions,
  answers,
  onAnswerChange,
  onContinue,
  onSkip,
  loading = false,
}) => {
  const answeredCount = Object.keys(answers).filter((key) =>
    answers[key]?.trim()
  ).length;
  const totalQuestions = questions.length;

  return (
    <Modal
      title={
        <div className="flex items-center space-x-2">
          <HelpCircle className="text-blue-600" size={24} />
          <span>Clarification Questions</span>
        </div>
      }
      open={visible}
      onCancel={onSkip}
      width={800}
      footer={null}
      className="clarification-modal"
    >
      <div className="space-y-6">
        {/* Header Info */}
        <Alert
          message="Improve Your SRS Quality"
          description={
            <div>
              <p className="mb-2">
                Our AI has identified some areas that could benefit from
                additional clarification. Answering these questions will help
                generate a more comprehensive and accurate SRS document.
              </p>
              <div className="flex items-center space-x-4 text-sm">
                <span>
                  Progress: {answeredCount}/{totalQuestions} answered
                </span>
                <span className="text-blue-600">
                  • All questions are optional
                </span>
              </div>
            </div>
          }
          type="info"
          showIcon
          icon={<Lightbulb className="text-blue-600" />}
        />

        {/* Questions */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {questions.map((question, index) => (
            <motion.div
              key={question.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="border border-gray-200 hover:border-blue-300 transition-colors">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mt-1">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <Text className="text-gray-800 font-medium">
                        {question.question}
                      </Text>
                    </div>
                  </div>

                  <div className="ml-9">
                    <TextArea
                      placeholder="Your answer (optional)..."
                      value={answers[question.id] || ""}
                      onChange={(e) =>
                        onAnswerChange(question.id, e.target.value)
                      }
                      rows={2}
                      className="rounded-lg"
                    />
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        <Divider />

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {answeredCount > 0 ? (
              <span className="text-green-600">
                ✓ {answeredCount} question{answeredCount !== 1 ? "s" : ""}{" "}
                answered
              </span>
            ) : (
              <span>No questions answered yet</span>
            )}
          </div>

          <Space size="middle">
            <Button
              icon={<SkipForward size={16} />}
              onClick={onSkip}
              disabled={loading}
              className="text-gray-600 border-gray-300"
            >
              Skip Questions
            </Button>

            <Button
              type="primary"
              icon={<ArrowRight size={16} />}
              onClick={onContinue}
              loading={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Continue Generation
            </Button>
          </Space>
        </div>

        {/* Help Text */}
        <div className="bg-gray-50 rounded-lg p-4">
          <Title level={5} className="text-gray-700 mb-2">
            💡 Tips for Better Answers
          </Title>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Be specific about your requirements and expectations</li>
            <li>• Include any constraints or limitations you're aware of</li>
            <li>• Mention any existing systems or integrations</li>
            <li>• Consider future scalability and growth plans</li>
          </ul>
        </div>
      </div>
    </Modal>
  );
};

export default ClarificationQuestions;
