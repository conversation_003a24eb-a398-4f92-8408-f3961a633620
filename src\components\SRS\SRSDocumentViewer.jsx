import { useState, useRef } from "react";
import {
  Card,
  Button,
  Space,
  Typography,
  Divider,
  message,
  Modal,
  Input,
} from "antd";
import {
  Download,
  Edit3,
  Save,
  Eye,
  FileText,
  Clock,
  User,
  Calendar,
  Copy,
} from "lucide-react";
import { motion } from "framer-motion";

const { Title, Text } = Typography;
const { TextArea } = Input;

// Enhanced SRS content formatting function for HTML and Markdown
const formatSRSContent = (content) => {
  if (!content) return "";

  // Check if content is already HTML (contains HTML tags)
  const isHTML = /<[a-z][\s\S]*>/i.test(content);

  if (isHTML) {
    // Content is already HTML - enhance it with additional styling
    return (
      content
        // Ensure proper container structure
        .replace(/^<!DOCTYPE html>[\s\S]*?<body[^>]*>/i, "")
        .replace(/<\/body>[\s\S]*?<\/html>$/i, "")
        .replace(/^<div class="container">/, "")
        .replace(/<\/div>$/, "")
        // Add responsive classes to existing elements
        .replace(
          /<table/g,
          '<table class="w-full border-collapse border border-gray-300 my-4"'
        )
        .replace(
          /<th/g,
          '<th class="border border-gray-300 px-4 py-2 bg-blue-600 text-white font-semibold"'
        )
        .replace(/<td/g, '<td class="border border-gray-300 px-4 py-2"')
        .replace(
          /<h1/g,
          '<h1 class="text-4xl font-bold mt-8 mb-6 text-gray-900 border-b-4 border-blue-600 pb-4"'
        )
        .replace(
          /<h2/g,
          '<h2 class="text-3xl font-bold mt-6 mb-4 text-gray-900 border-b-2 border-gray-300 pb-3"'
        )
        .replace(
          /<h3/g,
          '<h3 class="text-2xl font-semibold mt-5 mb-3 text-gray-800 border-l-4 border-purple-500 pl-3"'
        )
        .replace(
          /<h4/g,
          '<h4 class="text-xl font-semibold mt-4 mb-2 text-gray-800 border-l-4 border-yellow-500 pl-3"'
        )
        .replace(/<ul/g, '<ul class="list-disc list-inside my-4 space-y-2"')
        .replace(/<ol/g, '<ol class="list-decimal list-inside my-4 space-y-2"')
        .replace(/<li/g, '<li class="text-gray-700 leading-relaxed"')
        .replace(
          /<p(?![^>]*class)/g,
          '<p class="mb-4 text-gray-700 leading-relaxed"'
        )
        .replace(
          /<code(?![^>]*class)/g,
          '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono border"'
        )
        .replace(
          /<pre(?![^>]*class)/g,
          '<pre class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto my-4 border-l-4 border-green-500"'
        )
    );
  } else {
    // Content is Markdown - convert to HTML with styling
    return (
      content
        // Handle HTML tables (preserve them as-is)
        .replace(/<table[\s\S]*?<\/table>/gi, (match) => {
          return match.replace(/style="[^"]*"/gi, (styleMatch) => {
            return styleMatch + ' class="srs-table"';
          });
        })
        // Format markdown headings
        .replace(
          /#{6}\s(.*)/g,
          '<h6 class="text-lg font-semibold mt-6 mb-3 text-gray-800 border-l-4 border-blue-500 pl-3">$1</h6>'
        )
        .replace(
          /#{5}\s(.*)/g,
          '<h5 class="text-xl font-semibold mt-6 mb-3 text-gray-800 border-l-4 border-green-500 pl-3">$1</h5>'
        )
        .replace(
          /#{4}\s(.*)/g,
          '<h4 class="text-xl font-semibold mt-6 mb-3 text-gray-800 border-l-4 border-yellow-500 pl-3">$1</h4>'
        )
        .replace(
          /#{3}\s(.*)/g,
          '<h3 class="text-2xl font-semibold mt-8 mb-4 text-gray-800 border-l-4 border-purple-500 pl-3">$1</h3>'
        )
        .replace(
          /#{2}\s(.*)/g,
          '<h2 class="text-3xl font-bold mt-10 mb-6 text-gray-900 border-b-2 border-gray-300 pb-3">$1</h2>'
        )
        .replace(
          /#{1}\s(.*)/g,
          '<h1 class="text-4xl font-bold mt-12 mb-8 text-gray-900 border-b-4 border-blue-600 pb-4">$1</h1>'
        )
        // Format text styling
        .replace(
          /\*\*(.*?)\*\*/g,
          '<strong class="font-semibold text-gray-900 bg-yellow-50 px-1 rounded">$1</strong>'
        )
        .replace(/\*(.*?)\*/g, '<em class="italic text-gray-700">$1</em>')
        .replace(
          /`(.*?)`/g,
          '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono border">$1</code>'
        )
        // Format code blocks
        .replace(
          /```([\s\S]*?)```/g,
          '<pre class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto my-4 border-l-4 border-green-500"><code>$1</code></pre>'
        )
        // Format lists
        .replace(
          /^\s*[-*+]\s(.+)$/gm,
          '<li class="mb-2 text-gray-700 flex items-start"><span class="text-blue-500 mr-2">•</span>$1</li>'
        )
        .replace(
          /^\s*(\d+)\.\s(.+)$/gm,
          '<li class="mb-2 text-gray-700 flex items-start"><span class="text-blue-500 mr-2 font-semibold">$1.</span>$2</li>'
        )
        // Format paragraphs
        .replace(/\n\n/g, '</p><p class="mb-4 text-gray-700 leading-relaxed">')
        .replace(/^/, '<p class="mb-4 text-gray-700 leading-relaxed">')
        .replace(/$/, "</p>")
        // Clean up empty paragraphs
        .replace(/<p class="mb-4 text-gray-700 leading-relaxed"><\/p>/g, "")
    );
  }
};

const SRSDocumentViewer = ({ document, onSave, onClose, editable = true }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(document?.content || "");
  const [saving, setSaving] = useState(false);
  const contentRef = useRef(null);
  console.log(document, "document", editedContent);
  // Handle save
  const handleSave = async () => {
    try {
      setSaving(true);
      const updatedDocument = {
        ...document,
        content: editedContent,
        metadata: {
          ...document.metadata,
          lastModified: new Date().toISOString(),
          version: incrementVersion(document.metadata.version),
        },
      };

      await onSave(updatedDocument);
      setIsEditing(false);
      message.success("Document saved successfully!");
    } catch (error) {
      message.error("Failed to save document");
    } finally {
      setSaving(false);
    }
  };

  // Handle download
  const handleDownload = (format = "md") => {
    const content = isEditing ? editedContent : document.content;
    const filename = `${document.projectInfo.name.replace(
      /\s+/g,
      "_"
    )}_SRS.${format}`;

    let fileContent = content;
    let mimeType = "text/markdown";

    if (format === "txt") {
      // Convert markdown to plain text (basic conversion)
      fileContent = content
        .replace(/#{1,6}\s/g, "")
        .replace(/\*\*(.*?)\*\*/g, "$1")
        .replace(/\*(.*?)\*/g, "$1")
        .replace(/`(.*?)`/g, "$1");
      mimeType = "text/plain";
    } else if (format === "html") {
      // Basic markdown to HTML conversion
      fileContent = `
<!DOCTYPE html>
<html>
<head>
    <title>${document.projectInfo.name} - SRS Document</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
${content
  .replace(/#{6}\s(.*)/g, "<h6>$1</h6>")
  .replace(/#{5}\s(.*)/g, "<h5>$1</h5>")
  .replace(/#{4}\s(.*)/g, "<h4>$1</h4>")
  .replace(/#{3}\s(.*)/g, "<h3>$1</h3>")
  .replace(/#{2}\s(.*)/g, "<h2>$1</h2>")
  .replace(/#{1}\s(.*)/g, "<h1>$1</h1>")
  .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
  .replace(/\*(.*?)\*/g, "<em>$1</em>")
  .replace(/`(.*?)`/g, "<code>$1</code>")
  .replace(/\n\n/g, "</p><p>")
  .replace(/^/, "<p>")
  .replace(/$/, "</p>")}
</body>
</html>`;
      mimeType = "text/html";
    }

    const blob = new Blob([fileContent], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    message.success(`Document downloaded as ${format.toUpperCase()}`);
  };

  // Copy to clipboard
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(
        isEditing ? editedContent : document.content
      );
      message.success("Document copied to clipboard!");
    } catch (error) {
      message.error("Failed to copy document");
    }
  };

  // Increment version
  const incrementVersion = (version) => {
    const parts = version.split(".");
    parts[2] = (parseInt(parts[2]) + 1).toString();
    return parts.join(".");
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!document) {
    return null;
  }

  return (
    <Modal
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="text-blue-600" size={24} />
            <div>
              <span className="text-lg font-semibold">
                {document.projectInfo.name}
              </span>
              <div className="text-sm text-gray-500">
                SRS Document v{document.metadata.version}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock size={16} />
            <span>{document.metadata.estimatedReadTime} min read</span>
          </div>
        </div>
      }
      open={true}
      onCancel={onClose}
      width="90%"
      style={{ maxWidth: 1200 }}
      footer={null}
      className="srs-viewer-modal"
    >
      <div className="space-y-6">
        {/* Document Metadata */}
        <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Calendar size={16} className="text-blue-600" />
              <div>
                <div className="font-medium">Generated</div>
                <div className="text-gray-600">
                  {formatDate(document.metadata.generatedAt)}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <User size={16} className="text-green-600" />
              <div>
                <div className="font-medium">AI Provider</div>
                <div className="text-gray-600 capitalize">
                  {document.metadata.aiProvider}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FileText size={16} className="text-purple-600" />
              <div>
                <div className="font-medium">Word Count</div>
                <div className="text-gray-600">
                  {document.metadata.wordCount.toLocaleString()} words
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          <Space>
            {editable && (
              <Button
                icon={isEditing ? <Eye size={16} /> : <Edit3 size={16} />}
                onClick={() => setIsEditing(!isEditing)}
                className={
                  isEditing ? "bg-green-50 text-green-600 border-green-300" : ""
                }
              >
                {isEditing ? "Preview" : "Edit"}
              </Button>
            )}

            <Button icon={<Copy size={16} />} onClick={handleCopy}>
              Copy
            </Button>
          </Space>

          <Space>
            <Button
              icon={<Download size={16} />}
              onClick={() => handleDownload("md")}
            >
              Download MD
            </Button>

            <Button
              icon={<Download size={16} />}
              onClick={() => handleDownload("html")}
            >
              Download HTML
            </Button>

            <Button
              icon={<Download size={16} />}
              onClick={() => handleDownload("txt")}
            >
              Download TXT
            </Button>

            {isEditing && (
              <Button
                type="primary"
                icon={<Save size={16} />}
                onClick={handleSave}
                loading={saving}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Save Changes
              </Button>
            )}
          </Space>
        </div>

        <Divider />

        {/* Document Content */}
        <div className="min-h-96">
          {isEditing ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Title level={4}>Edit Document Content</Title>
                <Text className="text-sm text-gray-500">
                  Supports Markdown formatting
                </Text>
              </div>
              <TextArea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                rows={25}
                className="font-mono text-sm"
                placeholder="Edit your SRS document content here..."
              />
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="prose prose-blue max-w-none"
              ref={contentRef}
            >
              <div
                className="srs-content text-sm leading-relaxed"
                dangerouslySetInnerHTML={{
                  __html: formatSRSContent(document.content),
                }}
              />
            </motion.div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default SRSDocumentViewer;
