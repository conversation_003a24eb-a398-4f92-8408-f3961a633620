// SRS Form Static Data Constants

export const PLATFORM_OPTIONS = [
  'Web Application',
  'Mobile App (iOS)',
  'Mobile App (Android)',
  'Admin Dashboard',
  'Kiosk',
  'Desktop Application',
  'Browser Extension',
  'API Only'
];

export const HOSTING_OPTIONS = [
  'AWS',
  'Google Cloud Platform',
  'Microsoft Azure',
  'Heroku',
  'Netlify',
  'Vercel',
  'DigitalOcean',
  'Self-hosted',
  'Not decided'
];

export const TECHNOLOGY_OPTIONS = {
  frontend: ['React', 'Angular', 'Vue.js', 'Next.js', 'HTML/CSS/JS'],
  backend: ['Node.js', 'Python/Django', 'Java/Spring', 'PHP/Laravel', '.NET Core'],
  mobile: ['React Native', 'Flutter', 'Native iOS', 'Native Android', 'PWA'],
  database: ['MongoDB', 'PostgreSQL', 'MySQL', 'Firebase', 'SQLite'],
  storage: ['AWS S3', 'Google Cloud Storage', 'Azure Blob Storage', 'Firebase Storage']
};

export const INTEGRATION_OPTIONS = {
  payment: ['Stripe', 'PayPal'],
  authentication: ['Auth0', 'Firebase Auth'],
  maps: ['Google Maps', 'Mapbox'],
  analytics: ['Google Analytics', 'Mixpanel'],
  email: ['SendGrid', 'Mailchimp'],
  storage: ['AWS S3', 'Firebase Storage'],
  social: ['Facebook', 'Twitter', 'Instagram APIs'],
  communication: ['Twilio SMS', 'Socket.io Chat'],
  search: ['Algolia', 'Elasticsearch'],
  cms: ['Contentful', 'Strapi']
};

export const DEVELOPMENT_APPROACHES = [
  'Agile', 
  'Waterfall', 
  'Hybrid', 
  'Lean', 
  'DevOps', 
  'Not Decided'
];

export const COLLABORATION_TOOLS = {
  projectManagement: ['Jira', 'Trello', 'Asana', 'Monday.com', 'ClickUp'],
  versionControl: ['GitHub', 'GitLab', 'Bitbucket'],
  communication: ['Slack', 'Microsoft Teams', 'Discord'],
  documentation: ['Notion', 'Confluence'],
  design: ['Figma', 'Miro']
};

export const TEAM_ROLES = [
  'Project Manager',
  'Business Analyst', 
  'UX/UI Designer',
  'Frontend Developer',
  'Backend Developer',
  'Full Stack Developer',
  'Mobile Developer',
  'DevOps Engineer',
  'QA Engineer',
  'Database Administrator',
  'Technical Lead',
  'Product Owner',
  'Scrum Master',
  'Content Writer',
  'System Administrator'
];

export const PRIORITY_LEVELS = [
  { value: 'High', label: 'High', color: 'red' },
  { value: 'Medium', label: 'Medium', color: 'orange' },
  { value: 'Low', label: 'Low', color: 'green' }
];

export const EXPERIENCE_LEVELS = [
  'Junior (0-2 years)',
  'Mid-level (2-5 years)',
  'Senior (5-10 years)',
  'Expert (10+ years)'
];

export const FORM_STEPS = [
  {
    key: 'basic-info',
    title: 'Basic Project Information',
    description: 'Project name, description, and main goals',
    estimatedTime: 2
  },
  {
    key: 'platform-deployment',
    title: 'Platform & Deployment',
    description: 'Target platforms and hosting preferences',
    estimatedTime: 1
  },
  {
    key: 'technology-stack',
    title: 'Technology Stack',
    description: 'Frontend, backend, and database technologies',
    estimatedTime: 2
  },
  {
    key: 'user-roles',
    title: 'User Roles Definition',
    description: 'Define user roles and their permissions',
    estimatedTime: 2
  },
  {
    key: 'functional-modules',
    title: 'Functional Modules',
    description: 'Key features and module descriptions',
    estimatedTime: 3
  },
  {
    key: 'integrations',
    title: 'Third-Party Integrations',
    description: 'External services and APIs',
    estimatedTime: 1
  },
  {
    key: 'development-lifecycle',
    title: 'Development Lifecycle',
    description: 'Methodology and collaboration tools',
    estimatedTime: 1
  },
  {
    key: 'team-composition',
    title: 'Team Composition',
    description: 'Team roles and experience levels',
    estimatedTime: 2
  }
];

export const DEFAULT_FORM_VALUES = {
  userRoles: [{ name: '', actions: '' }],
  functionalModules: [{ description: '', priority: 'Medium' }],
  teamMembers: {},
  selectedPlatforms: []
};

export const STORAGE_KEYS = {
  SRS_FORM_DATA: 'srs_form_data',
  SRS_HISTORY: 'srs_history',
  USER_PREFERENCES: 'user_preferences'
};
